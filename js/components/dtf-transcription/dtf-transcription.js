(function () {

    function DTFTranscriptionCtrl(utils, Auth, $state) {

        "ngInject";

        if (!Auth.isAuthorized('dtftranscription')) {
            return $state.go( Auth.defaultRoute );
        }

        var ctrl = this;

        utils.appLoaded();

        ctrl.state = {

            name: 'scan',
            history: [],
            embroidery_info: false,

            is: function () {

                if (!arguments.length) return;

                if (arguments.length === 1) {
                    return arguments[0] === this.name;
                }

                if (arguments.length > 1) {

                    for (var i = 0; i < arguments.length; i++) {

                        if (arguments[i] === this.name) {
                            return true;
                        }

                    }

                    return false

                }

            },
            back: function () {

                var last = this.history[this.history.length - 1];

                this.set(last, true);

                if (last == 'scan') {
                    this.history = [];
                } else {
                    this.history.pop();
                }

                ctrl.state.embroidery_info = false;
            },
            set: function (name, forget) {

                if (!forget && this.name != name) {
                    this.history.push(this.name);
                }

                this.name = name;

                // emit event
                this._onChangeCallbacks.forEach(function(callback) {
                    callback();
                });

            },

            _onChangeCallbacks: [],
            onChange: function(callback) {
                if (angular.isFunction(callback)) {
                    this._onChangeCallbacks.push(callback);
                }
            }

        };

    }

    angular.module('printty')

        .component('dtfTranscriptionComponent', {
            require: {
                rootCtrl: '^^printtyApp'
            },
            controller: DTFTranscriptionCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/dtf-transcription/dtf-transcription.html'
        });


    /******************
     *******************
     * ******* LEFT ******
     ****************/


    function DTFTranscriptionLeftCtrl() {

        'ngInject';

        var ctrl = this;

        ctrl.$onInit = function () {
            ctrl.state = ctrl.DTFTranscriptionCtrl.state;
        };



    }

    angular.module('printty')

        .component('dtfTranscriptionLeft', {
            require: {
                DTFTranscriptionCtrl: '^^dtfTranscriptionComponent'
            },
            controller: DTFTranscriptionLeftCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/dtf-transcription/dtf-transcription-left.html'
        });


    /******************
     *******************
     * ******* RIGHT ******
     ****************/


    function DTFTranscriptionRightCtrl($element, DTFTranscription, Store, electron, $timeout, utils, ElectronUtils, $mdDialog, PrintApi) {

        'ngInject';

        var ctrl = this;

        ctrl.isElectron = !!electron;
        ctrl.tasks = [];
        ctrl.scannerInput = null;
        ctrl.qrCodeInput = null;
        var loader = new utils.loadCounter(
            function(isBlocking) {
                utils.listLoading.show(isBlocking);
            },
            function() {
                utils.listLoading.hide();
            },
            2
        );
        ctrl.$onInit = function () {

            ctrl.state = ctrl.DTFTranscriptionCtrl.state;
            ctrl.focusScanField();

            ctrl.state.onChange(function() {
                if (ctrl.state.is('scan', 'manual')) {
                    setDefault();
                }

                if (ctrl.state.is('scan')) {
                    ctrl.focusScanField();
                }
            });
        };

        ctrl.findQr = function () {
            if (!ctrl.qrCodeInput) {
                return;
            }
            process(ctrl.qrCodeInput);
        };

        ctrl.focusScanField = function () {
            $timeout(function () {
                $('#scanner-field').focus();
            });
        };

        ctrl.findQrScan = function () {
            utils.delay(function () {
                if (!ctrl.scannerInput) {
                    return;
                }
                process(ctrl.scannerInput);
            }, 250);
        };

        ctrl.clearInput = function () {
            ctrl.qrCodeInput = null;
            ctrl.errorText = null;
        };

        ctrl.end = function() {
            ctrl.clearInput();
            ctrl.scannerInput = null;
            ctrl.state.set('scan', true);
        }

        /******** helpers ***********/
        function setDefault() {
            ctrl.scannerInput = null;
            ctrl.errorText = null;
        }

        function preDownload (data) {
            if(data.transcription_again === true) {
                var confirm = $mdDialog.confirm({
                    template: utils.confirmDownloadDTFTranscriptionImage(),
                    controller: ['$scope', '$mdDialog', function ($scope, $mdDialog) {
                        $scope.cancelDownload = function () {
                            $mdDialog.cancel();
                        };
                        $scope.confirmDownload = function () {
                            $mdDialog.hide();
                        };
                    }]
                });
            }

            $mdDialog.show(confirm).then(function () {
                printQr(data.tasks.Task.id);
            });

        }

         function printQr(taskId) {

            if (!ctrl.isElectron) {
                alert('アプリ外で印刷不可です。');
                return;
            }

            PrintApi.getPrinterTypeLocal().then(function (data) {
                if (data && data['dtf-transcription'] && data['dtf-transcription'].qr_print == 0) {
                    runFetchQRCode(taskId, 2, {is_hidden_print: true});
                } else {
                    showPaperSizeDialog(taskId);
                }
            });
        }

        function showPaperSizeDialog(taskId) {
            var size;
            var confirm = $mdDialog.confirm({
                template: utils.choosePaperSizeTemplateQRTasks('印刷用紙サイズを選択してください。'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelChoose = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmChoose = function () {
                        size = $('.paper-size-radio:checked').val();
                        if(size) {
                            $mdDialog.hide();
                        }
                        return false;
                    };
                }]
            });

            $mdDialog.show(confirm).then(function () {
                runFetchQRCode(taskId, size,{});
            });
        }

        function runFetchQRCode(taskId, size, options) {
            var taskIds = taskId;
            loader.start(false);

            DTFTranscription.fetchQRCode({
                'task_id': taskIds,
                size: size,
            })
                .then(function (data) {
                    if (data && !options.is_hidden_print) {
                        electron.ipcRenderer.send('print-images', {
                            urls: data,
                            size: size
                        });
                    }
                    loader.stop();
                })
                .catch(function () {
                    loader.stop();
                });

            utils.delay(function() {
                ctrl.end();
                ctrl.focusScanField();
            }, 12000);
        }

        function process(code) {
            ctrl.is_qrCode = 1;

            if(code.indexOf('DTF-SUBLIMATION') !== -1){
                ctrl.is_qrCode = 0;
            }
            DTFTranscription.process({
                code: code,
                is_qrCode: ctrl.is_qrCode,
            })
                .then(
                    function(data) {
                        ctrl.state.set('finish', true);
                        ctrl.qrCodeInput = null;
                        ctrl.scannerInput = null;
                        if(data.transcription_again === true){
                            preDownload(data);
                        }else {
                            printQr(data.tasks.Task.id);
                        }

                    }
                )
                .catch(
                    function(error) {
                        ctrl.state.set('fail', true);
                        if(error.data.message) {
                            ctrl.errorText = error.data.message;
                        }
                    }
                );
        }
    }

    angular.module('printty')

        .component('dtfTranscriptionRight', {
            require: {
                DTFTranscriptionCtrl: '^^dtfTranscriptionComponent'
            },
            controller: DTFTranscriptionRightCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/dtf-transcription/dtf-transcription-right.html'
        });

})();