(function () {

    angular.module('printty')
        .component('factoryorderComponent', {
            require: {
                rootCtrl: '^^printtyApp'
            },
            controller: FactoryOrderCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/factory-order/factory-order.html'
        });

    function FactoryOrderCtrl($scope, Auth, utils, $state, Store) {

        "ngInject";

        var ctrl = this;

        ctrl.state = $state;
        ctrl.makers = [];
        ctrl.factories = [];
        ctrl.factorySelected = null;
        ctrl.factoryId = null;
        ctrl.initialState = ctrl.state.current.name;
        ctrl.date = moment().toDate();

        var subscriptions = [];

        $scope.$on('$stateChangeStart', function () { });

        ctrl.$onInit = function () {
            utils.appLoaded();
            Store.data('MakerCustomers').update(ctrl.makers);
            subscriptions.push(
                Store.data('MakerCustomers').subscribe(function (makers) { onMakersChange(makers); }),
                Store.data('FactoriesFilter').subscribe(function (data) {
                    ctrl.factories = data;
                }),
                Store.data('FactoryType').subscribe(function (data) {
                    ctrl.factoryId = data;
                })
            );

            Store.data('Image:HeaderDate').update(ctrl.date);
            Store.data('Image:HeaderFactory').update(ctrl.factoryId);
        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.createItemVendor = function () {
            Store.event('factoryorder:create:item').emit();
        };
        /*** private ****/
        function onMakersChange(makers) {
            ctrl.makers = makers;
        }

        ctrl.onFactoryChange = function() {
            Store.data('Image:HeaderFactory').update(ctrl.factorySelected);
            Store.data('FactoryType').update(ctrl.factorySelected);
        }

        ctrl.onDateChange = function () {
            Store.data('Image:HeaderDate').update(ctrl.date);
        };

    }

})();
