(function () {

  angular.module('printty')
    .component('adminIncomeClients', {
      controller: IncomeClientsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/income/clients.html'
    });
  
  function IncomeClientsCtrl(Customers, utils, Store, $timeout, Order, $state, Auth) {

    "ngInject";

    if (!Auth.isAuthorized('customers')) {
      return $state.go( Auth.defaultRoute );
    }
    
    var ctrl = this;
    
    /****** variables ********/
    ctrl.customers = [];

    ctrl.pagination = {
      perPage: 40,
      current: 0,

      searchQuery: null,
      lastSearch: null,

      empty: false,
      reload: false,

      loading: false,
      disabled: false
    };

    var subscriptions = [];

    var loader = new utils.loadCounter(
      function (isBlocking) {
        utils.listLoading.show(isBlocking);
      },
      function () {
        utils.listLoading.hide();
      },
      3
    );

    ctrl.customerDetails = {
      isShown: false,
      type: 'add',
      active: {}
    };
    
    ctrl.infiniteCustomers = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {

        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return ctrl.customers[index];

      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {
        fetchCustomers(index);
      }
    };

    /****** methods ******/
    ctrl.$onInit = function () {

      loader.start(true);

      fetchDeliveryPeriods();
      fetchDeliveryServices();

      subscriptions.push(

        Store.event('admin:create:customer')
          .subscribe(function () {
            ctrl.viewCreate();
          })

      );

    };

    ctrl.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };

    ctrl.reload = function() {
      ctrl.pagination.reload = true;
      ctrl.pagination.current = 0;
      ctrl.pagination.disabled = false;

      ctrl.customers = [];

      ctrl.infiniteCustomers.numLoaded_ = 0;
      ctrl.infiniteCustomers.toLoad_ = 0;
      ctrl.infiniteCustomers.getItemAtIndex(1);
    };

    ctrl.search = function () {
      if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
      ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
      ctrl.reload();
    };

    ctrl.liveSearch = function () {
      utils.delay(function() {
        ctrl.search();
      }, 350 );
    };

    ctrl.clearSearch = function () {
      ctrl.pagination.searchQuery = '';
      ctrl.search();
    };

    ctrl.viewCreate = function () {
      ctrl.customerDetails.type = 'add';
      ctrl.customerDetails.isShown = true;
      ctrl.customerDetails.active = {
        customerId: null
      };
    };
    
    ctrl.viewDetails = function (customer) {
      ctrl.customerDetails.type = 'details';
      ctrl.customerDetails.isShown = true;
      ctrl.customerDetails.active = {
        customerId: customer.Customer.id
      };
    };

    ctrl.customerDetails.onClose = function () {
      this.isShown = false;
    };

    ctrl.customerDetails.onCreate = function () {
      ctrl.reload();
    };

    ctrl.customerDetails.onUpdate = function (customer) {
      var originalCustomer = _.find(ctrl.customers, function (customerItem) {
        return customerItem.Customer.id == customer.Customer.id;
      });

      _.extend(originalCustomer.Customer, customer.Customer);
    };

    ctrl.customerDetails.onDelete = function (customerId) {
      _.remove(ctrl.customers, function(customer) {
        return customer.Customer.id == customerId;
      });
    };

    /****** private ******/
    function fetchCustomers(index) {

      if (ctrl.infiniteCustomers.toLoad_ >= index || ctrl.pagination.disabled) return;

      ctrl.pagination.disabled = true;

      if (ctrl.pagination.reload) {
        $timeout(function () {
          ctrl.pagination.loading = false;
          loader.start(false);
        });
      } else {
        $timeout(function () {
          ctrl.pagination.loading = true;
        });
      }

      ctrl.infiniteCustomers.toLoad_ += ctrl.pagination.perPage;

      Customers.fetchCustomers(
        {
          paging_size: ctrl.pagination.perPage,
          paging_offset: ctrl.pagination.current * ctrl.pagination.perPage,
          conditions_keywords: ctrl.pagination.searchQuery
        }
      ).then(
        function (data) {

          ctrl.pagination.current++;

          ctrl.infiniteCustomers.numLoaded_ = ctrl.infiniteCustomers.toLoad_ - ctrl.pagination.perPage + data.customers.length;
          ctrl.customers = ctrl.customers.concat(data.customers);

          ctrl.pagination.empty = !ctrl.customers.length;
          ctrl.pagination.reload = false;
          
          
          ctrl.pagination.disabled = data.total_count <= ctrl.customers.length;
          
          $timeout(function () {
            ctrl.pagination.loading = false;
            $timeout(function () {
              loader.stop();
            }, 120);
          });
          
        }
      );

    }

    function fetchDeliveryPeriods() {
      Order.fetchDeliveryPeriods()
        .then(function (data) {
          Store.data('DeliveryPeriods').update(data.periods);
        })
        .finally(function () {
          loader.stop();
        });
    }
    
    function fetchDeliveryServices() {
      Customers.fetchDeliveryServices()
        .then(function (data) {
          Store.data('DeliveryServices').update(data.deliveryservices);
        })
        .finally(function () {
          loader.stop();
        });
    }
    
  }

})();