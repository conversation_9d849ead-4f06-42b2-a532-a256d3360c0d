(function () {

  angular.module('printty')
    .component('adminProductionPrintersChart', {
      controller: ChartCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/production/printers/printers-chart.html',
      bindings: {
        targetDate: '<',
        typeId: '<',
        factoryId : '<',
      }
    });

  // todo: сделать ограничение на responsiveness (?) чтобы не было такое высокое: поискать можно ли выставалять пропрции
  function ChartCtrl($element, ProductionPrinters) {

    "ngInject";

    var ctrl = this;
    
    /******* variables *******/
    ctrl.dates = {
      current: moment(),
      start: moment(),
      end: moment()
    };
    
    ctrl.date = moment().toDate;
    ctrl.type = 'day';

    // 1: follow kenpin
    // 2: follow printer
    // 3: breakdown production
    ctrl.value_type = 1;
    ctrl.value_description = '商品';
    ctrl.type_description = '週';
    ctrl.period_total = {};

    var lineChart = null;
    var printersDatasets = [];

    var ctx = $element.find('canvas').get(0).getContext("2d");
    
    var daysNames = ['月曜日', '火曜日', '水曜日', '木曜日', '金曜日', '土曜日', '日曜日'];
    var daysOfMonth = [];

    var colors = ['rgba(206, 156, 220, 0.9)', 'rgba(165, 202, 251, 0.9)', 'rgba(88, 188, 116, 0.9)', 'rgba(255, 172, 100, 0.9)', 'rgba(208, 209, 117, 0.9)', 'rgba(255, 161, 161, 0.9)'];
    var colorsReturned = 0;
    
    var config = {
      type: 'line',
      data: {
        labels: daysNames,
        datasets: printersDatasets
      },
      options: {
        responsive: true,
        plugins: {
          labels: {
            boxWidth: 13,
            padding: 22
          },
          legend: {
            position: 'bottom',
          },
        },
        hover: {
          mode: 'label'
        },
        scales: {
          xAxes: [{display: true}],
          yAxes: [{display: true}]
        },
        elements: {
          line: {
            tension: 0.4
          }
        }
      }
    };
    
    /***** methods ******/
    
    ctrl.$onInit = function () {
      lineChart = new Chart(ctx, config);
    };
    
    ctrl.$onChanges = function () {
      
      ctrl.dates.current = moment(ctrl.targetDate);
      getDates();
      ctrl.date = ctrl.dates.start.clone().toDate();

      fetchData();
    };

    ctrl.search = function () {
      fetchData();
    }

    ctrl.goPrevWeek = function () {
      if (ctrl.type == "month"){
        ctrl.dates.end = ctrl.dates.start.subtract(1, 'm').startOf('month').clone();
        ctrl.dates.end.endOf('month');
      } else if (ctrl.type == "day") {
        ctrl.dates.start.subtract(1, 'w');
        ctrl.dates.end.subtract(1, 'w');
      }

      setLabels();
    };

    ctrl.goNextWeek = function () {
      if (ctrl.type == "month"){
        ctrl.dates.start = ctrl.dates.end.add(1, 'd').endOf('month').clone();
        ctrl.dates.start.startOf('month');
      } else if (ctrl.type == "day") {
        ctrl.dates.start.add(1, 'w');
        ctrl.dates.end.add(1, 'w');
      }
      setLabels();
    };

    ctrl.setValue = function (value) {
      ctrl.value_type = value;

      switch (value) {
        case 1:
          ctrl.value_description = '商品';
          break;
        case 2:
          ctrl.value_description = 'プリンター';
          break;
        case 3:
          ctrl.value_description = '生産面数内訳';
          break;
        default:
          ctrl.value_description = '商品';
          break;
      }
    };
    
    ctrl.setType = function (type) {
      ctrl.type = type;

      switch (type) {
        case 'day':
          ctrl.type_description = '週';
          break;
        case 'month':
          ctrl.type_description = '月';
          break;
        default:
          ctrl.type_description = '週';
          break;
      }

      getDates();
    };

    ctrl.isMonday = function (date) {
      var monday = moment(date).startOf('isoweek');
      return monday.isSame(date) ;
    };
    
    ctrl.onlyWeekendsPredicate = function(date) {
        var day = date.getDay();
        return day === 0 || day === 6;
    }

    ctrl.onDateSelect = function () {
      ctrl.dates.current = moment(ctrl.date);
      getDates();
      
      fetchData();
    };

    function getDates() {
      if (ctrl.type == "month"){
        getMonthDates()
      } else if (ctrl.type == "day") {
        getWeekDates();
      }
    }
    
    /**** private *****/
    function setLabels() {
      setLabelMonth(ctrl.dates.end.clone().format('D'));
      if (lineChart !== null)
      {
        if (ctrl.type == "month"){
          lineChart.data.labels = daysOfMonth;
          lineChart.update();
        } else if (ctrl.type == "day") {
          lineChart.data.labels = daysNames;
          lineChart.update();
        }
      }
    }
    
    /**** private *****/
    function setLabelMonth(day) {
      daysOfMonth = [];
      for (i = 1; i <= day; i++) {
        daysOfMonth.push(i + '日');
      }
    }

    /**** private *****/
    function getWeekDates() {
      ctrl.dates.start = ctrl.dates.current.clone().startOf('isoweek');
      ctrl.dates.end = ctrl.dates.current.clone().endOf('isoweek');
      setLabels()
    }

    /**** private *****/
    function getMonthDates() {
      ctrl.dates.start = ctrl.dates.current.clone().startOf('month');
      ctrl.dates.end = ctrl.dates.current.clone().endOf('month');
      setLabels()
    }

    function fetchData() {

      colorsReturned = 0;

      fetchGraph();
      fetchPeriodTotal();
    }
    
    function fetchGraph() {
      ProductionPrinters.fetchGraph({
        startDate: ctrl.dates.start.format('YYYY-MM-DD'),
        endDate: ctrl.dates.end.format('YYYY-MM-DD'),
        typeId: ctrl.typeId,
        value_type: ctrl.value_type,
        factory_id : ctrl.factoryId,
      })
        .then(function (data) {

          printersDatasets = data.grapth_values;
          config.data.datasets = printersDatasets;
          createChart();

        });

    }

    function fetchPeriodTotal() {

      for(total in ctrl.period_total){
        total.data = '-';
      }

      ProductionPrinters.fetchPeriodTotal({
        startDate: ctrl.dates.start.format('YYYY-MM-DD'),
        endDate: ctrl.dates.end.format('YYYY-MM-DD'),
        factory_id: ctrl.factoryId,
      })
        .then(function (data) {
          ctrl.period_total = data;
        });
    }

    function createChart() {
      
      _.forEach(config.data.datasets, function (dataset) {

        var background = randomColor(0.8);
        
        dataset.fill = false;
        dataset.borderColor = background;
        dataset.backgroundColor = background;
        dataset.pointBorderColor = background;
        dataset.pointBackgroundColor = background;
        dataset.pointBorderWidth = 1;
        
      });

      lineChart.update();
      
    }
    
    function randomColorFactor() {
      return Math.round(Math.random() * 255);
    }

    function randomColor(opacity) {

      if (colors.length >= colorsReturned + 1) {
        return colors[colorsReturned++];
      }

      return 'rgba(' + randomColorFactor() + ',' + randomColorFactor() + ',' + randomColorFactor() + ',' + (opacity || '.5') + ')';
    }
    
  }

})();