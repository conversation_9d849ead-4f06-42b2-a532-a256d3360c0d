(function() {

  angular.module('printty')
    .component('adminProductionTaskDetails', {
      controller: TaskDetailsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/production/tasks/task-details.html',
      bindings: {
        isShown: '<',
        active: '<',

        onClose: '&',
        onDelete: '&',
        guid: "@"
      }
    });

  function TaskDetailsCtrl($scope, utils, ProductionTasks, $mdDialog, $mdSidenav, $timeout) {

    "ngInject";

    /****** variables ******/
    var ctrl = this;

    ctrl.task = null;
    ctrl.sides = [];
    ctrl.loadingType = 'full';
    ctrl.activeStep = null;

    ctrl.qrShown = false;
    ctrl.clipSuccessMsg = false;

    var sidenav = {
      open: function() {
        $mdSidenav( ctrl.guid).open();
      },
      close: function() {
        $mdSidenav( ctrl.guid).close();
      }
    };

    var loader = new utils.loadCounter(
      function(type) {

        if (!type) {
          type = 'full';
        }

        ctrl.loadingType = type;
      },
      function() {
        ctrl.loadingType = 'stopped';
      }
    );

    /****** methods ******/

    ctrl.$postLink = function() {
        $mdSidenav( ctrl.guid).onClose(function() {
          $timeout(function() {
            ctrl.onClose();
          }, 400);
        });
    };

    ctrl.$onChanges = function(changes) {

      if ('isShown' in changes) {

        if (ctrl.isShown) {
          initComponent();
        } else {
          setDefault();
        }

      }
    };

    ctrl.confirmDelete = function(ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('タスクを削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function() {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function() {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteTask);

    };

    ctrl.toggleQr = function() {
      ctrl.qrShown = !ctrl.qrShown;
    };

    ctrl.close = function() {
      sidenav.close();
    };

    ctrl.onSelect = function(activeStep) {
      ctrl.activeStep = activeStep;
    };

    ctrl.onSuccessCopy = function(e) {
      e.clearSelection();

      ctrl.clipSuccessMsg = true;
      $timeout(function() {
        ctrl.clipSuccessMsg = false;
      }, 500);
    };

    /******** private **********/

    function initComponent() {

      loader.start();

      if (!ctrl.isAdd && ctrl.active.taskId) {
        fetchTask();
      }

      sidenav.open();

    }

    function fetchTask() {

      ProductionTasks.fetchTask(ctrl.active)
        .then(function(data) {
          ctrl.task = data;

          ctrl.sides = _.map(ctrl.task.steps, function(step) {

            return {
              title: step.TaskStep.title,
              image_url: step.TaskStep.image_url,
              code: step.TaskStep.code,
              user_print: step.TaskStep.user_print,
              printers_title: step.TaskStep.printers_title
            };

          });

          loader.stop();
        })
        .catch(function() {
          loader.stop();
        });
    }

    function deleteTask() {

      loader.start('partial');

      ProductionTasks.deleteTask(ctrl.task.Task.id)
        .then(function() {

          ctrl.onDelete({
            taskId: ctrl.task.Task.id
          });

          ctrl.close();
        })
        .catch(function() {
          loader.stop();
        });

    }

    function setDefault() {
      ctrl.task = null;
      ctrl.sides = [];
      ctrl.qrShown = false;
      ctrl.activeStep = null;
      loader.stop();
    }

  }

})();