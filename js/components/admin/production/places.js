(function () {

  angular.module('printty')
    .component('adminProductionPlaces', {
      controller: PlanPlacesCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/production/places.html'
    }).controller('GuideNoteController',GuideNoteController);
  
  function PlanPlacesCtrl(ProductionPlaces, utils, Store, electron, $timeout, $state, $mdDialog, Product, Customers,Factory) {

    "ngInject";
    
    var ctrl = this;

    /****** variables ********/
    ctrl.places = [];
    ctrl.placeOption = [];
    ctrl.after = [];

    ctrl.pagination = {
      perPage: 40,
      current: 0,
      clickAll: 0,

      date: null,
      type_id: null,

      searchQuery: $state.params.q,
      lastSearch: null,

      empty: false,
      reload: false,

      loading: false,
      disabled: false
    };

    ctrl.activeSort = null;
    ctrl.placesSorts = [
        {'key': 'created', 'value' : '生産中'},
        {'key': 'shipping_complite', 'value' : '発送済み'},
        {'key': '', 'value' : 'すべて'},
    ];

    ctrl.activeSort = ctrl.placesSorts[2];

    ctrl.setActiveSort = function (sort) {
        ctrl.activeSort = sort;
        ctrl.reload();
    };

    var allSelected = false;

    var subscriptions = [];

    var loader = new utils.loadCounter(
      function (isBlocking) {
        utils.listLoading.show(isBlocking);
      },
      function () {
        utils.listLoading.hide();
      },
      2
    );
      ctrl.selectedCustomer = [];
      ctrl.allCustomerSelected = false;
        ctrl.customerFilter = [
            {'Customer': {'id' : 0, 'title' : '会社名' },}
        ];

        ctrl.filterCustomer = function (id){
            if(id === 0) {
                selecteAllCustomter();
            }
            ctrl.selectedCustomer = getSelectedCustomter();
            ctrl.reload();
        };

        function getSelectedCustomter() {
            var selectedItems = [];
            _.forEach(ctrl.customerFilter, function (customer) {
                if (customer.selected && customer.Customer.id !== 0) {
                    selectedItems.push(customer.Customer.id);
                }
            });
            return selectedItems;
        }

        function selecteAllCustomter() {
            ctrl.allCustomerSelected = !ctrl.allCustomerSelected;
            _.forEach(ctrl.customerFilter, function (customer) {
                customer.selected = ctrl.allCustomerSelected;
            });
        }
      function getCustomers() {
          Customers.fetchCustomers().then(function(data) {
              ctrl.customerFilter = ctrl.customerFilter.concat(data.customers);
          });
      }
    var toPrint;

    ctrl.CSVType = {yamato:'yamato',sagawa:'sagawa',yupacket:'yupacket', ems:'ems', abroad:'abroad'};
    
    ctrl.isElectron = !!electron;

    ctrl.infinitePlaces = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {

        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return ctrl.places[index];

      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {
        fetchPlaces(index);
      }
    };

    ctrl.used_place = [];

    /****** methods ******/
    ctrl.$onInit = function () {

      loader.start(true);

      setType();
      fetchTypes();
      getFactories();
      getCustomers();

      subscriptions.push(

        Store.data('AdminProduction:HeaderDate')
          .subscribe(function (date) {
            ctrl.pagination.date = moment(date).format('YYYY-MM-DD');
            ctrl.reload();
          }),

        Store.event('sidebarActiveStatus')
          .subscribe(function (status) {
            ctrl.pagination.type_id = status;
            ctrl.reload();
          }),

        Store.event('CreatePlan:Update')
          .subscribe(function () {
            ctrl.reload();
          })

      );

      if (ctrl.isElectron) {
        
        electron.ipcRenderer.on('pdf-downloaded', function () {
          $timeout(function () {
            utils.globalLoading.hide();
          })
        });
        
      }

    };

      function setType() {
          var still_type = ['0', '1', '2', '3', '4', '7'];
          var type_id = 0;
          if (still_type.includes(localStorage.getItem('type_id')))
          {
              type_id = localStorage.getItem('type_id');
          }
          ctrl.pagination.type_id = type_id;
          Store.event('activeTypeId').emit(type_id);
      }

    ctrl.$onDestroy = function () {
      _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
    };

    ctrl.reload = function() {
      ctrl.showCount = false;
      ctrl.pagination.reload = true;
      ctrl.pagination.current = 0;
      ctrl.pagination.disabled = false;
      ctrl.pagination.clickAll = 0;
      ctrl.pagination.perPage = 40;

      ctrl.places = [];

      ctrl.infinitePlaces.numLoaded_ = 0;
      ctrl.infinitePlaces.toLoad_ = 0;
      ctrl.infinitePlaces.getItemAtIndex(1);
      ctrl.used_place = [];
    };

    ctrl.search = function () {
      if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
      ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
      ctrl.reload();
    };

    ctrl.liveSearch = function () {
      utils.delay(function() {
        ctrl.search();
      }, 350 );
    };

    ctrl.clearSearch = function () {
      ctrl.pagination.searchQuery = '';
      ctrl.search();
    };
    ctrl.onCompleteShippingUpdate = function (order) {
        var orderDetails = _.find(ctrl.places, function (place) {
            return place.Place.id == order.Place.id;
        });
        if(orderDetails){
            orderDetails.Order.memo = order.Order.memo;
        }
    };
    
    ctrl.getDelivery = function (has_preview) {
      utils.globalLoading.show();

      ProductionPlaces.fetchDeliveryNotes({
        date: ctrl.pagination.date,
        'places_ids[]': getSelected(),
        type_id: ctrl.pagination.type_id,
          has_preview: has_preview,
          factory_id: ctrl.factory,
      })
        .then(function () {
          alert('しました。処理次第メールでリンクが飛びます');
        })
        .finally(function () {
          utils.globalLoading.hide();
        })
      
    };

      ctrl.getDeliveryCategory = function (has_preview) {
          utils.globalLoading.show();

          ProductionPlaces.fetchDeliveryNotes({
              date: ctrl.pagination.date,
              'type_id[]': getSelectedPlace(),
              has_preview: has_preview,
              factory_id: ctrl.factory,
          })
              .then(function () {
                  alert('しました。処理次第メールでリンクが飛びます');
              })
              .finally(function () {
                  utils.globalLoading.hide();
              })

      };

      ctrl.prePrintGuideNote = function (ev) {

          utils.globalLoading.show();

          Product.guideNoteList()
          .then(function (data) {
              utils.globalLoading.hide();

              $mdDialog.show({
                  targetEvent: ev,
                  clickOutsideToClose: true,
                  templateUrl: 'views/admin/production/guide_note_dialog.html',
                  controller: 'GuideNoteController',
                  onComplete: afterShowAnimation,
                  locals: { products : data }
              })
              .then(function(product){
                  if(!product) return;

                  var size;
                  var confirm = $mdDialog.confirm({
                      template: utils.choosePaperSizeTemplate('印刷用紙サイズを選択してください。'),
                      controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                          $scope.cancelChoose = function () {
                              $mdDialog.cancel();
                          };
                          $scope.confirmChoose = function () {
                              size = $('.paper-size-radio:checked').val();
                              if(size) {
                                  $mdDialog.hide();
                              }
                              return false;
                          };
                      }]
                  }).targetEvent(ev);

                  $mdDialog.show(confirm).then(function () {
                      ctrl.printGuideNoteQRs(size, product);
                  });
              });

          })
          .catch(function () {
              utils.globalLoading.hide();
          });

      };

      // When the 'enter' animation finishes...
      function afterShowAnimation(scope, element, options) {
          // post-show code here: DOM element focus, etc.
      }

      ctrl.printGuideNoteQRs = function (size, product) {

          if (!ctrl.isElectron) {
              alert('プリンターは設定されてません。');
              return;
          }

          utils.globalLoading.show();

          Product.guideNoteQR(product.Product.id)
          .then(function (data) {
              if (data.length) {
                  electron.ipcRenderer.send('print-guide-note', {
                      url: data,
                      size: size,
                      quantity: product.Product.quantity
                  });
              } else {
                  utils.globalLoading.hide();
              }
          })
          .finally(function () {
              utils.globalLoading.hide();
          });

      };

    ctrl.changeStatus = function (ev, place) {
        var confirm = $mdDialog.confirm({
            template: utils.changeStatusTemplate(),
            controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                $scope.cancelDelete = function () {
                    $mdDialog.cancel();
                };
                $scope.confirmDelete = function () {
                    $mdDialog.hide();
                };
            }]
        }).targetEvent(ev);

        $mdDialog.show(confirm).then(function () {
            utils.globalLoading.show();
            ProductionPlaces.changeStatus(place.Place.order_id)
                .then(function () {
                    place.Place.status = place.Place.status == '発送済み' ? '生産中' : '発送済み';
                    utils.globalLoading.hide();
                })
                .finally(function () {
                    utils.globalLoading.hide();
                })
        });

    };

    ctrl.CSVdownload = function (type) {

      utils.globalLoading.show();
      
      ProductionPlaces.downloadCSV({
        date: ctrl.pagination.date,
        'places_ids': getSelected(),
        type_id: ctrl.pagination.type_id,
          factory_id: ctrl.factory,
      },type)
        .then(function () {
          utils.globalLoading.hide();
        })
        .catch(function () {
          utils.globalLoading.hide();
        });
      
    };

      ctrl.CSVdownload2 = function (type) {

          utils.globalLoading.show();

          ProductionPlaces.downloadCSV({
              date: ctrl.pagination.date,
              'type_id_2': getSelectedPlace(),
              factory_id: ctrl.factory,
          },type)
              .then(function () {
                  utils.globalLoading.hide();
              })
              .catch(function () {
                  utils.globalLoading.hide();
              });

      };

    ctrl.getPayments = function () {
        var data = getSelectedAfter();
        if(data.length == 0){
            return;
        }
      utils.globalLoading.show();

      ProductionPlaces.downloadPayments({
        date: ctrl.pagination.date,
        'places_ids[]': data
      })
        .then(function () {
          utils.globalLoading.hide();
        })
        .catch(function () {
          utils.globalLoading.hide();
        });

    };

      ctrl.getPaymentsAfter2 = function () {
          var data = getSelectedAfter();
          if(data.length == 0){
              return;
          }
          utils.globalLoading.show();

          ProductionPlaces.downloadPaymentsAfter2({
              date: ctrl.pagination.date,
              'places_ids[]': data

          })
              .then(function () {
                  utils.globalLoading.hide();
              })
              .catch(function () {
                  utils.globalLoading.hide();
              });

    };

    ctrl.CSVupload = function (file,type) {
      if (!file) return;
      
      var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);
      
      if ( fileType !== 'csv' ) {
        alert('File.UnexpectedFormat');
        return;
      }

      utils.globalLoading.show();
      
      ProductionPlaces.uploadCSV(file,type,ctrl.factory)
        .then(function () {
          utils.globalLoading.hide();
          ctrl.reload();
        })
        .catch(function () {
          utils.globalLoading.hide();
        });
      
    };
    
    ctrl.selectAll = function () {

        ctrl.pagination.clickAll = 1;
        fetchPlaces(ctrl.infinitePlaces.toLoad_ + 1);

      ctrl.places.forEach(function (place) {
        place.selected = !allSelected;
      });

      allSelected = !allSelected;
    };

      ctrl.manualShippingCompleteDetails = {
          isShown: false,
          type: 'add',
          active: {}
      };

      ctrl.manualShippingComplete = function (place) {
          if(place.Place.order_id){
              ctrl.manualShippingCompleteDetails.type = 'details';
              ctrl.manualShippingCompleteDetails.isShown = true;
              ctrl.manualShippingCompleteDetails.active = {
                  orderId: place.Place.order_id,
                  placeId : place.Place.id,
              };
          }
      };

      ctrl.onCompleteShippingClose = function () {
          ctrl.manualShippingCompleteDetails.isShown = false;
      };

      ctrl.onChange = function (){
          ctrl.reload();
      }

    /****** private ******/
    function fetchPlaces(index) {

      if (ctrl.infinitePlaces.toLoad_ >= index || ctrl.pagination.disabled) return;
      if(!ctrl.factory) return;

      ctrl.pagination.disabled = true;

      if (ctrl.pagination.reload) {
        ctrl.pagination.loading = false;
        loader.start(false);
      } else {
        ctrl.pagination.loading = true;
      }

      ctrl.infinitePlaces.toLoad_ += ctrl.pagination.perPage;


      if(ctrl.pagination.clickAll === 0){
          ctrl.pagination.perPage = 40;
      } else {
          ctrl.pagination.perPage = -1;
      }
      if(ctrl.pagination.type_id != 0){
          ctrl.showCount = true;
      }
        ctrl.last_use = 'なし';
        ctrl.total_use = 0;
        ctrl.gift_pink = 0;
        ctrl.gift_blue = 0;
        ctrl.gift_yellow = 0;

        ProductionPlaces.fetchPlaces(
        {
          paging_size: ctrl.pagination.perPage,
          paging_offset: ctrl.pagination.current * 40,
          date: ctrl.pagination.date,
          type_id: ctrl.pagination.type_id,
          keywords: ctrl.pagination.searchQuery,
          customer: ctrl.selectedCustomer.length > 0 ? ctrl.selectedCustomer.join(',') : null,
          sort_name: ctrl.activeSort ? ctrl.activeSort.key : null,
          select_all : (ctrl.pagination.clickAll === 1) ? 1 : 0,
          factory_id: ctrl.factory,
        }
      ).then(
        function (data) {

          allSelected = (ctrl.pagination.clickAll === 1) ? true : false;

          ctrl.pagination.current++;

          if(ctrl.pagination.clickAll === 1){
              ctrl.infinitePlaces.numLoaded_ = ctrl.infinitePlaces.toLoad_ - 40 + data.places.length;
          } else {
              ctrl.infinitePlaces.numLoaded_ = ctrl.infinitePlaces.toLoad_ - ctrl.pagination.perPage + data.places.length;
          }
          ctrl.places = ctrl.places.concat(data.places);
          ctrl.last_use = data.last_use;
          ctrl.total_use = data.total_use;
          ctrl.gift_display = data.gifts.gift_display;
          ctrl.gift_pink = data.gifts.gift_pink;
          ctrl.gift_blue = data.gifts.gift_blue;
          ctrl.gift_yellow = data.gifts.gift_yellow;

          ctrl.pagination.empty = !ctrl.places.length;
          ctrl.pagination.loading = false;
          ctrl.pagination.reload = false;

          ctrl.pagination.disabled = data.total_count <= ctrl.places.length;
          ctrl.used_place = data.used_place;

          loader.stop();
        }
      );

    }
    
    function fetchTypes() {

      ProductionPlaces.fetchTypes().then(function (data) {
        var statuses = _.flatMap(data.types, function (type) {
          return type.PlaceType;
        });
        ctrl.placeOption.push(statuses);
        Store.data('statuses').update(statuses);

        loader.stop();
      });
      
    }
      function getFactories() {
          Factory.fetchFactoryUser().then(function(data){
              ctrl.factories = data.factories;
              ctrl.factory = data.factorySelected;
          });
      }

      Customers.fetchDeliveryServices()
          .then(function(data) {
              Store.data('DeliveryServices').update(data.deliveryservices);
          });

    function getSelected() {

      var selectedItemsArr = [];

      _.forEach(ctrl.places, function (place) {

        if (place.selected) {
            selectedItemsArr.push(place.Place.id);
        }

      });
      
        var arrayToString = JSON.stringify(Object.assign({}, selectedItemsArr));
        var selectedItems = JSON.parse(arrayToString);

      return selectedItems;

    }
      function getSelectedAfter() {

          var selectedItems = [];

          _.forEach(ctrl.places, function (place) {

              if (place.selected) {
                  selectedItems.push(place.Place.id);
              }
          });

          return selectedItems;

      }

    function getSelectedPlace() {

          var selectedPlaces = [];

          _.forEach(ctrl.placeOption[0], function (placeOption) {

              if (placeOption.selected) {
                  selectedPlaces.push(placeOption.id);
              }

          });

          return selectedPlaces;

      }

  }

    function GuideNoteController($scope, $mdDialog, products) {
        // Assigned from construction <code>locals</code> options...

        $scope.products = products;

        $scope.closeDialog = function() {
            // Easily hides most recent dialog shown...
            // no specific instance reference is needed.
            $mdDialog.hide(null);
        };

        $scope.printGuideNote = function(product){
            if(isPositiveInteger(product.Product.quantity)) {
                $mdDialog.hide(product);
            }
        };

        function isPositiveInteger(s)
        {
            var i = +s; // convert to a number
            if (i <= 0) return false; // make sure it's positive
            if (i != ~~i) return false; // make sure there's no decimal part
            return true;
        }
    }

})();