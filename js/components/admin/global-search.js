(function() {

  angular.module('printty')
    .component('adminGlobalSearch', {
      controller: adminGlobalSearchCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/admin/global-search.html',
      bindings: {
        open: '=openOn'
      }
    })
    .directive('slideable', function() {
      return {
        restrict: 'C',
        compile: function(element, attr) {
          // wrap tag
          var contents = element.html();
          element.html('<div class="slideable_content" style="margin:0 !important; padding:0 !important" >' + contents + '</div>');


          return function postLink(scope, element, attrs) {
            // default properties
            attrs.duration = (!attrs.duration) ? '0.3s' : attrs.duration;
            attrs.easing = (!attrs.easing) ? 'ease-in-out' : attrs.easing;
            element.css({
              'overflow': 'hidden',
              'transitionProperty': 'height',
              'transitionDuration': attrs.duration,
              'transitionTimingFunction': attrs.easing
            });
          };

        }
      };
    })
    .directive('slideToggle', function() {
      return {
        restrict: 'A',
        link: function(scope, element, attrs) {
          var target = document.querySelector(attrs.slideToggle);
          attrs.expanded = true;
          element.bind('click', function() {
            var content = target.querySelector('.slideable_content');
            if (!attrs.expanded) {
              content.style.border = '1px solid rgba(0,0,0,0)';
              var y = content.clientHeight;
              content.style.border = 0;
              target.style.height = y + 'px';
            } else {
              target.style.height = '0px';
            }
            attrs.expanded = !attrs.expanded;
          });
        }
      }
    });

  function adminGlobalSearchCtrl($scope,$element, $timeout, $state, utils, SearchService, Customers, Store) {

    "ngInject";

    var ctrl = this;
    ctrl.globalSearch = globalSearch;
    ctrl.$onInit = $onInit;
    ctrl.showMoreInSection = showMoreInSection;
    ctrl.viewDetailsTask = viewDetailsTask;

    ctrl.results = [];

    ctrl.showOrders = ctrl.showCustomers = ctrl.showTasks = ctrl.showPrinters = ctrl.showPlaces = true;

    ctrl.pagination = {
      sections: {
        customers: {current: 0},
        tasks: {current: 0},
        places: {current: 0},
        printers: {current: 0},
        orders: {current: 0}
      },
      perPage: 10,
      current: 0,

      date: null,
      type_id: null,

      empty: false,

      loading: false,
      disabled: false
    };

    var $body = angular.element('body');
    var component = {
      show: function() {
        $element.addClass('shown');
        $element.find('#globalSearch').focus();
        $body.addClass('not-scrollable-x');
      },
      hide: function() {
        $element.removeClass('shown').addClass('hiding');
        $timeout(function() {
          $element.removeClass('hiding');
          $body.removeClass('not-scrollable-x');
        }, 300)
      }
    };

    /******* methods **********/
    ctrl.open = function () {
      component.show();
    };

    ctrl.close = function () {
      ctrl.searchQuery = undefined;
      ctrl.results = [];
      ctrl.pagination.empty = false;
      component.hide();
    };

    function $onInit() {
      fetchDeliveryServices();
    }

    var loader = new utils.loadCounter(
      function (type) {
        if (!type) type = 'full';
        ctrl.loadingType = type;
      },
      function () {
        ctrl.loadingType = 'stopped';
      }
    );


    function reload() {
      ctrl.pagination.disabled = false;

      ctrl.results = [];
      fetchSearchResults();
    }

    // TASKS

    ctrl.taskDetails = {
      isShown: false,
      active: {}
    };

    function viewDetailsTask(task) {
      ctrl.taskDetails.isShown = true;
      ctrl.taskDetails.active = {
        taskId: task.Task.id
      };
    }

    ctrl.taskDetails.onTaskClose = function() {
      ctrl.taskDetails.isShown = false;
    };

    ctrl.taskDetails.onTaskDelete = function(taskId) {
      _.remove(ctrl.results.tasks.object, function(task) {
        return task.Task.id == taskId;
      });
    };

    // END TASKS

    // CUSTOMERS
    ctrl.customerDetails = {
      isShown: false,
      type: 'details',
      active: {}
    };


    ctrl.viewDetailsCustomer = function(customer) {
      ctrl.customerDetails.type = 'details';
      ctrl.customerDetails.isShown = true;
      ctrl.customerDetails.active = {
        customerId: customer.Customer.id
      };
    };

    ctrl.customerDetails.onClose = function() {
      this.isShown = false;
    };

    ctrl.customerDetails.onUpdate = function(customer) {
      var originalCustomer = _.find(ctrl.results.customers.object, function(customerItem) {
        return customerItem.Customer.id == customer.Customer.id;
      });

      _.extend(originalCustomer.Customer, customer.Customer);
    };

    ctrl.customerDetails.onDelete = function(customerId) {
      _.remove(ctrl.results.customers.object, function(customer) {
        return customer.Customer.id == customerId;
      });
    };
    // END CUSTOMERS

    function showMoreInSection(sectionName) {
      ctrl.pagination.disabled = false;
      ctrl.pagination.sections[sectionName].current += ctrl.pagination.perPage;
      fetchSearchResults(sectionName);
    }


    function globalSearch() {

      utils.delay(function() {
        $scope.$apply(
          function() {
            if (!ctrl.searchQuery){
              ctrl.results = [];
              ctrl.pagination.empty = false;
              return;
            }
            loader.start('partial');
            reload();
          }
        );
      }, 500 );

    }

    /****** private ******/
    function fetchSearchResults(type) {

      if (ctrl.pagination.disabled) return;

      ctrl.pagination.disabled = true;
      ctrl.pagination.loading = true;
      ctrl.pagination.empty = false;


      var send_data = {
        keywords: ctrl.searchQuery
      };
      if (type) {
        for (var p in ctrl.pagination.sections) {
          send_data[p + "_offset"] = 0;
          send_data[p + "_limit"] = 0;
        }
        send_data[type + "_offset"] = ctrl.pagination.sections[type].current;
        send_data[type + "_limit"] = ctrl.pagination.perPage;
      } else {
        for (var p in ctrl.pagination.sections) {
          send_data[p + "_offset"] = ctrl.pagination.sections[p].current;
          send_data[p + "_limit"] = ctrl.pagination.perPage;
        }
      }

      SearchService.search(send_data).then(
        function(data) {

          if (type) {
            if (!ctrl.results[type])
              ctrl.results[type] = [];
            if (data[type])
              ctrl.results[type].object = ctrl.results[type].object.concat(data[type].object);
          }
          else
            ctrl.results = data;

          ctrl.pagination.empty = !ctrl.results;
          ctrl.pagination.loading = false;


        }
      ).finally(loader.stop);

    }


    function fetchDeliveryServices() {
      Customers.fetchDeliveryServices()
        .then(function(data) {
          Store.data('DeliveryServices').update(data.deliveryservices);
        })
    }
  }

})();