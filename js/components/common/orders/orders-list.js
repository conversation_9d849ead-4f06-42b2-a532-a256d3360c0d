(function() {

  angular.module('printty')
    .component('ordersList', {
      controller: OrdersListCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/common/orders/orders-list.html',
      bindings: {
        open: '=openOn'
      }
    });
  
  function OrdersListCtrl($element, $timeout, utils, CustomersOrders) {

    "ngInject";
    
    var ctrl = this;
    
    ctrl.orders = [];
    ctrl.loadingType = 'full';
    ctrl.customer  = null;
    
    var $body = angular.element('body');
    
    var component = {
      show: function () {
        $element.addClass('shown');
        $body.addClass('not-scrollable-x');
      },
      hide: function () {
        $element.removeClass('shown').addClass('hiding');
        $timeout(function () {
          $element.removeClass('hiding');
          $body.removeClass('not-scrollable-x');
          setDefault();
        }, 300)
      }
    };

    var loader = new utils.loadCounter(
      function (type) {
        if (!type) type = 'full';
        ctrl.loadingType = type;
      },
      function () {
        ctrl.loadingType = 'stopped';
      }
    );

    ctrl.pagination = {
      perPage: 40,
      current: 0,

      searchQuery: null,
      lastSearch: null,

      empty: false,
      reload: false,

      loading: false,
      disabled: true
    };
    
    ctrl.infiniteOrders = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {

        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return ctrl.orders[index];

      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {
        fetchOrders(index);
      }
    };
    
    /******* methods **********/
    ctrl.open = function (customer) {
      ctrl.customer = customer;
      ctrl.pagination.disabled = false;

      ctrl.infiniteOrders.numLoaded_ = 0;
      ctrl.infiniteOrders.toLoad_ = 0;
      ctrl.infiniteOrders.getItemAtIndex(1);
      
      loader.start();
      
      component.show();
    };
    
    ctrl.close = function () {
      component.hide();
    };

    ctrl.reload = function() {
      ctrl.pagination.reload = true;
      ctrl.pagination.current = 0;
      ctrl.pagination.disabled = false;

      ctrl.orders = [];

      ctrl.infiniteOrders.numLoaded_ = 0;
      ctrl.infiniteOrders.toLoad_ = 0;
      ctrl.infiniteOrders.getItemAtIndex(1);
    };

    ctrl.search = function () {
      if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
      ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
      ctrl.reload();
    };

    ctrl.liveSearch = function () {
      utils.delay(function() {
        ctrl.search();
      }, 350 );
    };
    
    /***** private *******/
    function fetchOrders(index) {
      if (ctrl.infiniteOrders.toLoad_ >= index || ctrl.pagination.disabled) return;

      ctrl.pagination.disabled = true;

      if (ctrl.pagination.reload) {
        ctrl.pagination.loading = false;
        loader.start('partial');
      } else {
        $timeout(function () {
          ctrl.pagination.loading = true;
        });
      }

      ctrl.infiniteOrders.toLoad_ += ctrl.pagination.perPage;

      CustomersOrders.fetchOrders(
        {
          paging_size: ctrl.pagination.perPage,
          paging_offset: ctrl.pagination.current * ctrl.pagination.perPage,
          keywords: ctrl.pagination.searchQuery,
          customer_id: ctrl.customer.Customer.id
        }
      )
        .then(
          function (data) {
            
            ctrl.pagination.current++;
  
            ctrl.infiniteOrders.numLoaded_ = ctrl.infiniteOrders.toLoad_ - ctrl.pagination.perPage + data.orders.length;
            ctrl.orders = ctrl.orders.concat(data.orders);
  
            ctrl.pagination.empty = !ctrl.orders.length;
            ctrl.pagination.loading = false;
            ctrl.pagination.reload = false;
  
            ctrl.pagination.disabled = !data.orders.length || (data.total_count <= ctrl.orders.length);
          }
        )
        .finally(function () {
          loader.stop();
        });
    }

    function setDefault() {
      ctrl.orders = [];
      ctrl.customer = null;
      
      ctrl.pagination.disabled = true;
      ctrl.pagination.current = 0;
    }
    
  }
  
})();
