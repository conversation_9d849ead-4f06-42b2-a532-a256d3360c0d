(function () {

  function PrintersCtrl(Printer, Store, utils, Auth,Factory) {

    "ngInject";

    utils.appLoaded();
    
    var ctrl = this;
    
    ctrl.printers = [];
    ctrl.selectedFactory = [];
    ctrl.activeFilter = null;
    var pagination = {
      init: true,
      perPage: 40,
      current: 0,
      type_id: null,

      searching: false,
      searchQuery: '',
      lastSearch: '',
      searchFailed: false,

      busy: false,
      end: false
    };
    ctrl.pagination = pagination;
    
    var subscription;
    ctrl.factoryFilter = [
      {'Factory': {'id' : 0, 'title' : 'すべて' }}
    ];

    pagination.reload = function () {

      this.init = true;
      this.end = false;
      this.busy = false;
      this.current = 0;

      ctrl.printers = [];

      ctrl.infinitePrinters.numLoaded_ = 0;
      ctrl.infinitePrinters.toLoad_ = 0;
      ctrl.infinitePrinters.getItemAtIndex(1);
    };

    ctrl.setActiveFilter = function (filter) {
      ctrl.activeFilter = filter;
      pagination.reload();
    };

    ctrl.filterFactory = function (id){
      if(id === 0) {
        selecteAllFactory();
      }
      ctrl.selectedFactory = getSelectedFactory();
      pagination.reload();
    };

    /** Search block **/
    pagination.liveSearch = function () {
      utils.delay(function() {
        pagination.search();
      }, 350 );
    };

    pagination.search = function () {
      if (pagination.lastSearch === pagination.searchQuery) return;

      pagination.lastSearch = pagination.searchQuery;
      pagination.searching = true;
      pagination.reload();
    };

    pagination.clearSearch = function () {
      pagination.searchQuery = '';
      pagination.search();
    };

    function getSelectedFactory() {

      var selectedItems = [];

      _.forEach(ctrl.factoryFilter, function (factory) {

        if (factory.selected && factory.Factory.id !== 0) {
          selectedItems.push(factory.Factory.id);
        }

      });

      return selectedItems;

    }

    function selecteAllFactory() {

      ctrl.allFactorySelected = !ctrl.allFactorySelected;

      _.forEach(ctrl.factoryFilter, function (factory) {
        factory.selected = ctrl.allFactorySelected;
      });

    }

    ctrl.activeFactory = ctrl.factoryFilter[0];

    ctrl.$onInit = function () {
      getTypes();
      getPrinterTypesColors();
      getFactories();
      subscription = Store.event('sidebarActiveStatus')
        .subscribe(function (status) {
          onStatusChange(status)
        });
      
      ctrl.parentCtrl.onPrinterCreate = ctrl.onPrinterCreate;
    };

    ctrl.$onDestroy = function () {
      subscription.unsubscribe();
    };

    ctrl.infinitePrinters = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {

        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return ctrl.printers[index];

      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {

        if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

        pagination.busy = true;

        if (pagination.init) {
          utils.listLoading.show(!pagination.searching);
        } else {
          utils.moreItemsLoad.show()
        }

        this.toLoad_ += pagination.perPage;

        pagination.current++;

        Printer.list(
          {
            paging_size: pagination.perPage,
            paging_offset: (pagination.current - 1) * pagination.perPage,
            conditions_keywords: pagination.searchQuery,
            type_id: pagination.type_id,
            factory: ctrl.selectedFactory.length > 0 ? ctrl.selectedFactory.join(',') : null,
          }
        ).then(
          function (data) {
            pagination.init = false;
            pagination.searching = false;

            ctrl.infinitePrinters.numLoaded_ = ctrl.infinitePrinters.toLoad_ - pagination.perPage + data.printers.length;
            ctrl.printers = ctrl.printers.concat(data.printers);

            pagination.searchFailed = !ctrl.printers.length;
            pagination.end = ctrl.printers.length >= data.total_count;
            pagination.busy = false;

            utils.listLoading.hide();
            utils.moreItemsLoad.hide();
          }
        );

      }
    };
    
    ctrl.onPrinterDelete = function (printerToDelete) {
      _.remove(ctrl.printers, function(printer) {
        return printer.Printer.id == printerToDelete.Printer.id;
      });
    };

    ctrl.onPrinterCreate = function () {
      pagination.reload();
    };
    
    /*** private ****/
    function getTypes() {
      Printer.typesAll().then(function (data) {
        var statuses = _.flatMap(data.types, function (type) {
          return type.PrinterType;
        });

        Store.data('statuses').update(statuses);
      });
    }

    function getFactories() {

      Printer.factoryAll().then(function(data) {
        ctrl.factoryFilter = ctrl.factoryFilter.concat(data.factories);
      });

    }

    function onStatusChange(status) {
      pagination.type_id = status;
      pagination.reload();
    }
    
    function getPrinterTypesColors() {
      Printer.types().then(function (data) {
        Store.data('PrinterTypes').update(data.types);
      });
      Printer.colors().then(function (data) {
        Store.data('PrinterColors').update(data.colors);
      });
      Factory.fetchFactoryUser().then(function (data) {
        Store.data('Factories').update(data.factories);
      });
    }
    
  }

  angular.module('printty')
    .component('referencePrinters', {
      require: {
        parentCtrl: '^^referenceComponent'
      },
      controller: PrintersCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/printers.html'
    });

})();