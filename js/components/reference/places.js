(function () {

  function PlacesCtrl(Place, utils, Store, Auth,Factory) {

    "ngInject";

    utils.appLoaded();

    var ctrl = this;

    ctrl.places = [];
    ctrl.selectedFactory = [];
    ctrl.activeFilter = null;
    var pagination = {
      init: true,
      perPage: 40,
      type_id: null,

      searching: false,
      searchQuery: '',
      lastSearch: '',
      searchFailed: false,

      busy: false,
      end: false
    };
    ctrl.pagination = pagination;

    var subscription;
    ctrl.factoryFilter = [
      {'Factory': {'id' : 0, 'title' : 'すべて' }}
    ];

    pagination.reload = function () {

      this.init = true;
      this.end = false;
      this.busy = false;

      ctrl.places = [];

      ctrl.infinitePlaces.numLoaded_ = 0;
      ctrl.infinitePlaces.toLoad_ = 0;
      ctrl.infinitePlaces.getItemAtIndex(1);
    };

    ctrl.setActiveFilter = function (filter) {
      ctrl.activeFilter = filter;
      pagination.reload();
    };

    ctrl.filterFactory = function (id){
      if(id === 0) {
        selecteAllFactory();
      }
      ctrl.selectedFactory = getSelectedFactory();
      pagination.reload();
    };

    /** Search block **/
    pagination.liveSearch = function () {
      utils.delay(function() {
        pagination.search();
      }, 350 );
    };

    pagination.search = function () {
      if (pagination.lastSearch === pagination.searchQuery) return;

      pagination.lastSearch = pagination.searchQuery;
      pagination.searching = true;
      pagination.reload();
    };

    pagination.clearSearch = function () {
      pagination.searchQuery = '';
      pagination.search();
    };

    function getSelectedFactory() {

      var selectedItems = [];

      _.forEach(ctrl.factoryFilter, function (factory) {

        if (factory.selected && factory.Factory.id !== 0) {
          selectedItems.push(factory.Factory.id);
        }

      });

      return selectedItems;

    }

    function selecteAllFactory() {

      ctrl.allFactorySelected = !ctrl.allFactorySelected;

      _.forEach(ctrl.factoryFilter, function (factory) {
        factory.selected = ctrl.allFactorySelected;
      });

    }

    ctrl.activeFactory = ctrl.factoryFilter[0];

    ctrl.$onInit = function () {

      getTypes();
      getFactories();
      subscription = Store.event('sidebarActiveStatus')
        .subscribe(function (status) {
          onStatusChange(status)
        });

      ctrl.parentCtrl.onPlaceCreate = ctrl.onPlaceCreate;

      $('#fileInput').on('change', function (e) {
        ctrl.CSVupload(e.target.files[0]);
      });

      $('#fileInput').on('click', function () {
        this.value = null;
      });
    };

    ctrl.$onDestroy = function () {
      subscription.unsubscribe();
    };

    ctrl.infinitePlaces = {
      numLoaded_: 0,
      toLoad_: 0,
      getItemAtIndex: function(index) {

        if (index > this.numLoaded_) {
          this.fetchMoreItems_(index);
          return null;
        }

        return ctrl.places[index];

      },
      getLength: function() {
        return this.numLoaded_ + 3;
      },
      fetchMoreItems_: function(index) {

        if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

        pagination.busy = true;

        if (pagination.init) {
          utils.listLoading.show(!pagination.searching);
        } else {
          utils.moreItemsLoad.show()
        }

        this.toLoad_ += pagination.perPage;

        Place.list(
          {
            paging_size: pagination.perPage,
            paging_offset: ctrl.places.length,
            conditions_keywords: pagination.searchQuery,
            type_id: pagination.type_id,
            factory: ctrl.selectedFactory.length > 0 ? ctrl.selectedFactory.join(',') : null,
          }
        ).then(
          function (data) {
            pagination.init = false;
            pagination.searching = false;

            ctrl.infinitePlaces.numLoaded_ = ctrl.infinitePlaces.toLoad_ - pagination.perPage + data.places.length;
            ctrl.places = ctrl.places.concat(data.places);

            pagination.searchFailed = !ctrl.places.length;
            pagination.end = ctrl.places.length >= data.total_count;
            pagination.busy = false;

            utils.listLoading.hide();
            utils.moreItemsLoad.hide();
          }
        );

      }
    };

    // fetch types place details
    Place.types().then(function (data) {
      Store.data('PlaceTypes').update(data.types);
    });

    Factory.fetchFactoryUser().then(function (data) {
      Store.data('Factories').update(data.factories);
    });

    ctrl.onPlaceDelete = function (placeToDelete) {
      _.remove(ctrl.places, function(place) {
        return place.Place.id == placeToDelete.Place.id;
      });
    };

    ctrl.onPlaceCreate = function () {
      pagination.reload();
    };

    ctrl.CSVupload = function (file) {
      if (!file) {
        $('#fileInput').click();
        return;
      }

      var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);
      if ( fileType !== 'csv' ) {
        alert('File.UnexpectedFormat');
        return;
      }

      utils.globalLoading.show();

      Place.uploadCSV(file)
          .then(function () {
            utils.globalLoading.hide();
            pagination.reload();
          })
          .catch(function () {
            utils.globalLoading.hide();
          });

    };

    /**** helpers *****/
    function getTypes() {
      Place.typesAll().then(function (data) {
        var statuses = _.flatMap(data.types, function (type) {
          return type.PlaceType;
        });

        Store.data('statuses').update(statuses);
      });
    }

    function getFactories() {

      Place.factoryAll().then(function(data) {
        ctrl.factoryFilter = ctrl.factoryFilter.concat(data.factories);
      });

    }

    function onStatusChange(status) {
      pagination.type_id = status;
      pagination.reload();
    }

  }

  angular.module('printty')

    .component('referencePlaces', {
      require: {
        parentCtrl: '^^referenceComponent'
      },
      controller: PlacesCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/places.html'
    });

})();