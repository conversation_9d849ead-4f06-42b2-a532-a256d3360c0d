(function () {

  angular.module('printty')
    .component('productColorSideDetails', {
      controller: ProductColorSideDetailsCtrl,
      controllerAs: 'vm',
      templateUrl: 'views/reference/product-details/product-color-side.html',
      bindings: {
        loadingType: '=',
        onClose: '&',
        onCreate: '&',
        onUpdate: '&',
        onDelete: '&',
        isShown: '<ngShow',
        type: '<',
        active: '=',
        showButton: '=',
      }
    });

  function ProductColorSideDetailsCtrl($scope, utils, $mdDialog, ProductColor) {
    "ngInject";

    /****** variables ******/
    var ctrl = this;

    ctrl.side = null;
    ctrl.plates = [];
    
    ctrl.isAdd = true;
    ctrl.showMode = true;

    var sideForm,
        sideClone;
    
    var loader = new utils.loadCounter(
      function (type) {
        
        if (!type) {
          type = 'full';
        }
        
        ctrl.loadingType = type;
      },
      function () {
        ctrl.loadingType = 'stopped';
      },
      2
    );

    /****** methods ******/
    ctrl.$onChanges = function (changes) {
      if ('isShown' in changes) {

        if (ctrl.isShown) {
          initComponent();
        } else {
          setDefault();
        }

      }
    };

    ctrl.$postLink = function () {
      sideForm = $scope.productColorSideForm;
    };

    ctrl.edit = function () {

      if (ctrl.showMode) {
        sideClone = _.cloneDeep(ctrl.side);
        ctrl.showMode = false;
      } else {
        ctrl.side = sideClone;
        setFormPristine();
        ctrl.showMode = true;
      }

    };

    ctrl.update = function () {

      sideForm.$setSubmitted();

      if (sideForm.$invalid) return;

      if  ( _.isEqual(ctrl.side, sideClone) ) {
        setFormPristine();
        ctrl.showMode = true;
        return;
      }

      ctrl.loadingType = 'partial';

      ProductColor.side.update({
        ProductColorSide: ctrl.side
      })
        .then(function () {
          updateOriginal();
          setFormPristine();
          ctrl.showMode = true;

          ctrl.loadingType = 'stopped';
        })
        .catch(function () {
          ctrl.loadingType = 'stopped';
        });

    };

    ctrl.save = function () {

      sideForm.$setSubmitted();

      if (sideForm.$invalid) return;

      ctrl.loadingType = 'partial';

      ProductColor.side.create({
        ProductColor: {
          id: ctrl.active.colorId
        },
        ProductColorSide: ctrl.side
      })
        .then(function (data) {

          ctrl.onCreate();

          _.extend(ctrl.side, {
            id: data.ProductColorSide.id,
            is_main: false
          });

          setFormPristine();

          ctrl.isAdd = false;
          ctrl.showMode = true;

          ctrl.loadingType = 'stopped';
        })
        .catch(function () {
          ctrl.loadingType = 'stopped';
          ctrl.close();
        });

    };

    ctrl.confirmDelete = function (ev) {

      var confirm = $mdDialog.confirm({
        template: utils.deleteTemplate('サイドを削除しますか？'),
        controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
          $scope.cancelDelete = function () {
            $mdDialog.cancel();
          };
          $scope.confirmDelete = function () {
            $mdDialog.hide();
          };
        }]
      }).targetEvent(ev);

      $mdDialog.show(confirm).then(deleteSide);

    };

    ctrl.close = function () {
      ctrl.onClose();
    };

    ////////// Linked Plates //////////
    ctrl.linkPlateBlock = {
      isShown: false,
      type: 'add',
      active: null
    };

    ctrl.linkPlateBlock.show = function (plate) {
      this.type = 'details';

      this.activeItem = {
        linkedPlateId: plate.ProductColorSideSizeLinkedPlate.id,
        sideId: ctrl.side.id
      };

      this.isShown = true;
    };

    ctrl.linkPlateBlock.add = function () {
      this.type = 'add';

      this.activeItem = {
        linkedPlateId: null,
        sideId: ctrl.side.id
      };

      this.isShown = true;
    };

    ctrl.linkPlateBlock.onClose = function () {
      this.isShown = false;
    };

    ctrl.linkPlateBlock.onCreate = function () {
      fetchLinkedPlates();
    };

    ctrl.linkPlateBlock.onUpdate = function (plateData) {

      var originalPlate = _.find(ctrl.plates, function (plate) {
        return plate.ProductColorSideSizeLinkedPlate.id == plateData.ProductColorSideSizeLinkedPlate.id;
      });
      
      _.extend(originalPlate, plateData);

    };

    ctrl.linkPlateBlock.onDelete = function (plateId) {
      _.remove(ctrl.plates, function (plate) {
        return plate.ProductColorSideSizeLinkedPlate.id == plateId;
      });
    };

    /******** private **********/

    function initComponent() {

      switch (ctrl.type) {
        case 'details':
          ctrl.isAdd = false;
          ctrl.showMode = true;
          break;
        case 'add':
        default:
          ctrl.isAdd = true;
          ctrl.showMode = false;
      }

      if (!ctrl.isAdd && ctrl.active.sideId) {
        loader.start('partial');
        fetchSide();
        fetchLinkedPlates();
      }

    }

    function fetchSide() {

      ProductColor.side.details(ctrl.active.sideId)
        .then(function (data) {
          ctrl.side = data.ProductColorSide;
          loader.stop();
        })
        .catch(function () {
          loader.stop();
        });

    }
    
    function fetchLinkedPlates() {

      ProductColor.side.linkedPlates.fetchPlates(ctrl.active.sideId)
        .then(function (data) {
          ctrl.plates = data.plates;
          loader.stop();
        })
        .catch(function () {
          loader.stop();
        });
      
    }

    function updateOriginal() {
      var sideData = _.cloneDeep(ctrl.side);

      ctrl.onUpdate({
        side: sideData
      });
      
    }

    function deleteSide() {

      ctrl.loadingType = 'partial';

      ProductColor.side.delete(ctrl.side.id)
        .then(function () {

          ctrl.onDelete({
            sideId: ctrl.side.id
          });

          ctrl.close();

          ctrl.loadingType = 'stopped';
        })
        .catch(function () {
          ctrl.close();
          ctrl.loadingType = 'stopped';
        });

    }

    function setDefault() {
      ctrl.isAdd = true;
      ctrl.side = null;
      ctrl.plates = [];
      
      loader.default();
      
      setFormPristine();
    }

    function setFormPristine() {
      if (sideForm) {
        sideForm.$setPristine();
        sideForm.$setUntouched();
      }
    }

  }

})();