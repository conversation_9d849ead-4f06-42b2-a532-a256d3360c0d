(function () {

    angular.module('printty')
        .component('referenceAndonDetails', {
            controller: AndonDetailsCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/reference/andon/andon-details.html',
            bindings: {
                isShown: '<',
                onClose: '&',
                onCreate: '&',
                onUpdate: '&',
                onDelete: '&',
                type: '<',
                active: '='
            }
        });

    function AndonDetailsCtrl($scope, $mdDialog, $mdSidenav, $timeout, utils, Store, AndonApi) {
        "ngInject";

        var ctrl = this,
            subscriptions = [],
            andonClone,
            andonForm,
            sidenav = {
                open: function () {
                    ctrl.checkAndon = null;
                    ctrl.checkfactoryType = null;
                    $mdSidenav('reference-andon-details').open();
                },
                close: function () {
                    $mdSidenav('reference-andon-details').close();
                }
            };

        ctrl.isAdd = true;
        ctrl.showMode = true;

        ctrl.loadingType = 'full';

        ctrl.$onInit = function() {
            subscriptions.push(
                Store.data('GeneralPurpose').subscribe(function (data) {
                    if(data){
                        ctrl.factoryTypes = data.factory_types;
                    }
                })
            );
        };

        ctrl.$onDestroy = function () {
            subscriptions.forEach(function (subscription) {
                subscription.unsubscribe();
            })
        };

        /****** methods ******/
        ctrl.$postLink = function () {
            andonForm = $scope.andonFrameForm;

            $mdSidenav('reference-andon-details').onClose(function () {
                $timeout(function () {
                    ctrl.onClose();
                }, 400);
            });
        };

        ctrl.$onChanges = function (changes) {
            if ('isShown' in changes) {

                if (ctrl.isShown) {
                    initComponent();
                } else {
                    setDefault();
                }

            }
        };


        ctrl.edit = function () {
            ctrl.checkAndon = null;
            ctrl.checkfactoryType = null;

            if (ctrl.showMode) {
                andonClone = _.cloneDeep(ctrl.andon);
                ctrl.showMode = false;
            } else {
                ctrl.andon = andonClone;
                setFormPristine();
                ctrl.showMode = true;
            }
        };

        ctrl.update = function () {
            andonForm.$setSubmitted();

            if(typeof andonForm.title.$viewValue == "undefined" || andonForm.title.$viewValue === '' || andonForm.title.$viewValue == null) {
                ctrl.checkAndon = true;
            }else {
                ctrl.checkAndon = false;
            }

            if(typeof andonForm.factory_type.$viewValue == "undefined" || andonForm.factory_type.$viewValue === '' || andonForm.factory_type.$viewValue == null) {
                ctrl.checkfactoryType = true;
            }else {
                ctrl.checkfactoryType = false;
            }

            if (_.isEqual(ctrl.andon, andonClone)) {
                ctrl.showMode = true;
                setFormPristine();
                return;
            }
            if(!ctrl.checkAndon && !ctrl.checkfactoryType) {
                ctrl.loadingType = 'partial';
                ctrl.showMode = true;
                AndonApi.updateAndon(ctrl.andon).then(function () {
                    updateAndonList();
                    setFormPristine();
                    ctrl.loadingType = 'stopped';
                }, function () {
                    ctrl.loadingType = 'stopped';
                });
            }
        };

        ctrl.save = function () {
            andonForm.$setSubmitted();

            if(typeof andonForm.title.$viewValue == "undefined" || andonForm.title.$viewValue === '' || andonForm.title.$viewValue == null) {
                ctrl.checkAndon = true;
            }else {
                ctrl.checkAndon = false;
            }

            if(typeof andonForm.factory_type.$viewValue == "undefined" || andonForm.factory_type.$viewValue === '' || andonForm.factory_type.$viewValue == null) {
                ctrl.checkfactoryType = true;
            }else {
                ctrl.checkfactoryType = false;
            }


            if(!ctrl.checkAndon && !ctrl.checkfactoryType) {
                ctrl.loadingType = 'partial';

                AndonApi.createAndon(ctrl.andon)
                    .then(function () {
                        ctrl.onCreate();
                        ctrl.close();
                    })
                    .catch(function () {
                        ctrl.loadingType = 'stopped';
                    });
            }
        };

        ctrl.delete = function (ev) {

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('生産セルを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }]
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function() {

                ctrl.loadingType = 'partial';

                AndonApi.deleteAndon(ctrl.andon.id).then(
                    function () {
                        ctrl.onDelete({
                            andonId: ctrl.andon.id
                        });
                        ctrl.close();

                    }, function () {
                        ctrl.loadingType = 'stopped';
                    }
                );

            });

        };

        ctrl.close = function () {
            sidenav.close();
        };


        /******** private **********/
        function initComponent() {
            switch (ctrl.type) {
                case 'details':
                    ctrl.loadingType = 'full';
                    ctrl.isAdd = false;
                    ctrl.showMode = true;
                    break;
                case 'add':
                default:
                    ctrl.loadingType = 'stopped';
                    ctrl.isAdd = true;
                    ctrl.showMode = false;
            }

            if (!ctrl.isAdd && ctrl.active.andonId) {
                fetchAndon();
            }

            sidenav.open();
        }

        function fetchAndon() {
            ctrl.loadingType = 'full';
            AndonApi.fetchAndon(ctrl.active.andonId)
                .then(function (data) {
                    ctrl.andon = data.andon;
                    ctrl.loadingType = 'stopped';
                })
                .catch(function () {
                    ctrl.loadingType = 'stopped';
                });
        }

        function updateAndonList() {
            var originalAndon = _.cloneDeep(ctrl.andon);

            originalAndon.factory_type = _.find(ctrl.factoryTypes, function (type) {
                return type.Factory.id == ctrl.andon.factory_id;
            }).Factory.title;

            ctrl.onUpdate({
                andon: originalAndon
            });
        }

        function setDefault() {
            ctrl.andon = null;
            ctrl.isAdd = true;
            originalAndon = null;

            setFormPristine();
            ctrl.loadingType = 'full';
        }

        function setFormPristine() {
            if (andonForm) {
                andonForm.$setPristine();
                andonForm.$setUntouched();
            }
        }

    }

})();