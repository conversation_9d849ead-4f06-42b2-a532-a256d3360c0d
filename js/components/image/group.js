(function () {

    angular.module('printty')
        .component('imageGroup', {
            controller: ImageGroupCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/image/group.html'
        });

    function ImageGroupCtrl(Auth, $state, Store, utils, electron , Image, $mdDialog, $timeout) {

        "ngInject";

        var ctrl = this;

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';
        var subscriptions = [];
        ctrl.total_waiting = 0;
        ctrl.total_error = 0;
        var allSelected = false;
        var scan = true;

        ctrl.images = [];

        var pagination = {
            init: true,
            perPage: 40,
            status_id: 0,
            type_id: 0,

            date: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.images = [];

            ctrl.infiniteImages.numLoaded_ = 0;
            ctrl.infiniteImages.toLoad_ = 0;
            ctrl.infiniteImages.getItemAtIndex(1);
        };

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.imageDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infiniteImages = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.images[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                Image.fetchGroupImages(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.images.length,
                        conditions_keywords: pagination.searchQuery,
                        status_id: pagination.status_id,
                        type_id: pagination.type_id,
                        date: pagination.date
                    }
                ).then(
                    function (data) {
                        allSelected = false;
                        pagination.init = false;
                        pagination.searching = false;
                        $("#scan-field").blur();
                        ctrl.infiniteImages.numLoaded_ = ctrl.infiniteImages.toLoad_ - pagination.perPage + data.images.length;
                        ctrl.images = ctrl.images.concat(data.images);
                        ctrl.total_waiting = data.total_waiting;
                        ctrl.total_error = data.total_error;

                        pagination.searchFailed = !ctrl.images.length;
                        pagination.end = ctrl.images.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {
            getStatuses();
            getTypes();

            Store.data('customers').update([]);

            subscriptions.push(

                Store.event('sidebarActiveStatus')
                    .subscribe(function (status) {
                        ctrl.pagination.status_id = status;
                        ctrl.reload();
                    })

            );

            subscriptions.push(

                Store.event('sidebarActiveTypes')
                    .subscribe(function (type) {
                        ctrl.pagination.type_id = type;
                        ctrl.reload();
                    })

            );

            subscriptions.push(

                Store.data('Image:HeaderDate')
                    .subscribe(function (date) {
                        ctrl.pagination.date = moment(date).format('YYYY-MM-DD');
                        ctrl.reload();
                    })
            );

            document.addEventListener("keypress", function(e) {
                if (e.target.tagName !== "INPUT") {
                    var input = document.querySelector("#scan-field");
                    input.focus();
                    input.value = e.key;
                    e.preventDefault();
                }
            });

            // ctrl.infiniteImages.numLoaded_ = 0;
            // ctrl.infiniteImages.toLoad_ = 0;
            // ctrl.infiniteImages.getItemAtIndex(1);

            utils.appLoaded();
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        function getStatuses() {
            var statuses = [
                {id:0, title:'すべて'},
                {id:1, title:'完了'},
                {id:2, title:'待機中'},
                {id:3, title:'処理中'},
                {id:4, title:'エラー'}
            ];
            Store.data('statuses').update(statuses);
        }

        function getTypes() {
            var types = [
                {id : 0, title: 'すべて'},
                {id : 'uv', title: 'UV'},
                {id : 'vakuum', title: '真空'},
                {id : 'laser', title: 'レーザー'},
                {id : 'cp', title: 'カッティングプロッター'},
            ];
            Store.data('types').update(types);
        }

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function() {
            ctrl.pagination.init = true;
            ctrl.pagination.end = false;
            ctrl.pagination.busy = false;
            scan = true;

            ctrl.images = [];

            ctrl.infiniteImages.numLoaded_ = 0;
            ctrl.infiniteImages.toLoad_ = 0;
            ctrl.infiniteImages.getItemAtIndex(1);
        };

        ctrl.reloadImage = function(){

            var selected;

            selected = getSelected();

            if (_.isArray(selected) && !selected.length) {
                return;
            }

            var params = {
                task_group_step_id : selected
            };

            utils.globalLoading.show();

            Image.reloadGroupImage(params).then(
                function () {

                    utils.globalLoading.hide();
                    ctrl.reload();
                },
                function() {
                    utils.globalLoading.hide();
                }
            );
        };

        function getSelected() {

            var selectedItems = [];

            _.forEach(ctrl.images, function (image) {

                if (image.selected) {
                    selectedItems.push(image.TaskGroupStep.id);
                }

            });

            return selectedItems;

        }

        ctrl.selectAll = function () {

            _.forEach(ctrl.images, function (image) {
                image.selected = !allSelected;
            });

            allSelected = !allSelected;

        };

        ctrl.showLargeImage = function (ev, image) {
            var confirm = $mdDialog.show({
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };

                    $scope.image_large = image.TaskGroupStep.medium_preview_image_url;
                }],
                templateUrl: 'views/image/image-dialog.html',
                clickOutsideToClose:true,
            });
        };

        ctrl.deleteImage = function(ev) {
            var selected;

            selected = getSelected();

            if (_.isArray(selected) && !selected.length) {
                return;
            }

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('タスクを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }],
                clickOutsideToClose:true,
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function () {

                var params = {
                    task_group_step_id : selected
                };

                utils.globalLoading.show();

                Image.deleteGroupImage(params).then(
                    function () {

                        utils.globalLoading.hide();
                        ctrl.reload();
                    },
                    function() {
                        utils.globalLoading.hide();
                    }
                );
            });
        }

        ctrl.getPrintImage = function(ev){

            var selected;

            selected = getSelected();

            if (_.isArray(selected) && !selected.length) {
                selected = null;
            }

            var confirm = $mdDialog.confirm({
                template: utils.changeStatusTemplate('印刷画像をメールで送信します'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }],
                clickOutsideToClose:true,
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function () {
                if(pagination.type_id == 0){
                    var params = {
                        task_group_step_id : selected,
                        date: pagination.date,
                        type: 'group'
                    };
                } else {
                    var params = {
                        task_group_step_id : selected,
                        date: pagination.date,
                        type: 'group',
                        type_group: pagination.type_id,
                    };
                }

                utils.globalLoading.show();

                Image.getPrintImage(params)
                    .then(function () {
                        alert('しました。処理次第メールでリンクが飛びます');
                    })
                    .finally(function () {
                        utils.globalLoading.hide();
                    })

            });
        }
    }

})();