(function () {

    angular.module('printty')
        .component('imageQrgroup', {
            controller: ImageQrgroupCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/image/qr-group.html'
        });

    function ImageQrgroupCtrl(Auth, $state, Store, utils, electron , ImageQrgroup, $mdDialog) {

        "ngInject";

        var ctrl = this;

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';
        var subscriptions = [];
        ctrl.total_waiting = 0;
        ctrl.total_error = 0;
        var allSelected = false;

        ctrl.qrgroups = [];

        var pagination = {
            init: true,
            perPage: 40,
            status_id: 0,

            date: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.qrgroups = [];

            ctrl.infiniteQrgroups.numLoaded_ = 0;
            ctrl.infiniteQrgroups.toLoad_ = 0;
            ctrl.infiniteQrgroups.getItemAtIndex(1);
        };

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.imageDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infiniteQrgroups = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.qrgroups[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                ImageQrgroup.fetchSentQrgroup(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.qrgroups.length,
                        conditions_keywords: pagination.searchQuery,
                        status_id: pagination.status_id,
                        customer_id: pagination.customer_id,
                        date: pagination.date,
                    }
                ).then(
                    function (data) {
                        allSelected = false;
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteQrgroups.numLoaded_ = ctrl.infiniteQrgroups.toLoad_ - pagination.perPage + data.qrgroups.length;
                        ctrl.qrgroups = ctrl.qrgroups.concat(data.qrgroups);
                        ctrl.total_waiting = data.total_waiting;
                        ctrl.total_error = data.total_error;

                        pagination.searchFailed = !ctrl.qrgroups.length;
                        pagination.end = ctrl.qrgroups.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {
            getStatuses();

            Store.data('types').update([]);

            subscriptions.push(

                Store.event('sidebarActiveStatus')
                    .subscribe(function (status) {
                        ctrl.pagination.status_id = status;
                        ctrl.reload();
                    })

            );

            subscriptions.push(

                Store.data('Image:HeaderDate')
                    .subscribe(function (date) {
                        ctrl.pagination.date = moment(date).format('YYYY-MM-DD');
                        ctrl.reload();
                    })

            );

            utils.appLoaded();
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        function getStatuses() {
            var statuses = [
                {id:0, title:'すべて'},
                {id:1, title:'完了'},
                {id:2, title:'待機中'},
                {id:3, title:'処理中'},
                {id:4, title:'エラー'}
            ];
            Store.data('statuses').update(statuses);
        }

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function() {
            ctrl.pagination.init = true;
            ctrl.pagination.end = false;
            ctrl.pagination.busy = false;

            ctrl.qrgroups = [];

            ctrl.infiniteQrgroups.numLoaded_ = 0;
            ctrl.infiniteQrgroups.toLoad_ = 0;
            ctrl.infiniteQrgroups.getItemAtIndex(1);
        };

        ctrl.reloadQrgroup = function(){

            var selected;

            selected = getSelected();

            if (_.isArray(selected) && !selected.length) {
                return;
            }

            var params = {
                qrgroup_id : selected
            };

            utils.globalLoading.show();

            ImageQrgroup.reloadQrgroup(params).then(
                function () {

                    utils.globalLoading.hide();
                    ctrl.reload();
                },
                function() {
                    utils.globalLoading.hide();
                }
            );
        };

        function getSelected() {

            var selectedItems = [];

            _.forEach(ctrl.qrgroups, function (qrgroup) {

                if (qrgroup.selected) {
                    selectedItems.push(qrgroup.TaskGroupQRSheetQueue.id);
                }

            });

            return selectedItems;

        }

        ctrl.selectAll = function () {

            _.forEach(ctrl.qrgroups, function (qrgroup) {
                qrgroup.selected = !allSelected;
            });

            allSelected = !allSelected;

        };

        ctrl.deleteQrgroup = function(ev) {
            var selected;

            selected = getSelected();

            if (_.isArray(selected) && !selected.length) {
                return;
            }

            var confirm = $mdDialog.confirm({
                template: utils.deleteTemplate('タスクを削除しますか？'),
                controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
                    $scope.cancelDelete = function () {
                        $mdDialog.cancel();
                    };
                    $scope.confirmDelete = function () {
                        $mdDialog.hide();
                    };
                }],
                clickOutsideToClose:true,
            }).targetEvent(ev);

            $mdDialog.show(confirm).then(function () {
                var params = {
                    qrgroup_id : selected
                };

                utils.globalLoading.show();

                ImageQrgroup.deleteQrgroup(params).then(
                    function () {

                        utils.globalLoading.hide();
                        ctrl.reload();
                    },
                    function() {
                        utils.globalLoading.hide();
                    }
                );
            });
        }

    }

})();