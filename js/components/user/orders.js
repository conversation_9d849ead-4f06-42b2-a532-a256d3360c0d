(function () {
  
    function UserOrdersCtrl(Auth, $state, Order, utils, Store, $mdDialog) {

      "ngInject";

      if (!Auth.isAuthorized('orders')) {
        return $state.go( Auth.defaultRoute );
      }
      
      utils.appLoaded();

      var ctrl = this;

      ctrl.orders = [];
      ctrl.limit = null;
      ctrl.date = moment().toDate();

      ctrl.pagination = {
        init: true,
        perPage: 40,
        current: 0,
        status_id: null,

        searching: false,
        searchQuery: '',
        lastSearch: '',
        searchFailed: false,

        busy: false,
        end: false
      };

      // wrap func in object to pass to parent by link (tmp: use events)
      ctrl.newOrder = {
        add: function () {}
      };
      
      var pagination = ctrl.pagination,
          subscription;

      ctrl.$onInit = function() {
        
        getStatuses();
        getLimit();
        fetchDeliveryPeriods();

        ctrl.parentCtrl.newOrder = ctrl.newOrder;

        ctrl.parentCtrl.downloadCSV = ctrl.downloadCSV;
        
        subscription = Store.event('sidebarActiveStatus')
          .subscribe(function (status) {
            onStatusChange(status)
          });
        
      };
      
      ctrl.$onDestroy = function () {
        subscription.unsubscribe();
      };
      
      pagination.reload = function () {

        this.init = true;
        this.end = false;
        this.busy = false;
        this.current = 0;

        ctrl.orders = [];

        ctrl.infiniteOrders.numLoaded_ = 0;
        ctrl.infiniteOrders.toLoad_ = 0;
        ctrl.infiniteOrders.getItemAtIndex(1);
      };
      
      /** Search block **/
      pagination.liveSearch = function () {
        utils.delay(function() {
          pagination.search();
        }, 350 );
      };

      pagination.search = function () {
        if (pagination.lastSearch === pagination.searchQuery) return;

        pagination.lastSearch = pagination.searchQuery;
        pagination.searching = true;
        pagination.reload();
      };

      pagination.clearSearch = function () {
        pagination.searchQuery = '';
        pagination.search();
      };

      ctrl.infiniteOrders = {
        numLoaded_: 0,
        toLoad_: 0,
        getItemAtIndex: function(index) {

          if (index > this.numLoaded_) {
            this.fetchMoreItems_(index);
            return null;
          }

          return ctrl.orders[index];

        },
        getLength: function() {
          return this.numLoaded_ + 3;
        },
        fetchMoreItems_: function(index) {

          if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

          pagination.busy = true;

          if (pagination.init) {
            utils.listLoading.show(!pagination.searching);
          } else {
            utils.moreItemsLoad.show()
          }

          this.toLoad_ += pagination.perPage;

          pagination.current++;

          Order.list(
            {
              paging_size: pagination.perPage,
              paging_offset: (pagination.current - 1) * pagination.perPage,
              keywords: pagination.searchQuery,
              status_id: pagination.status_id
            }
          ).then(
            function (data) {
              pagination.init = false;
              pagination.searching = false;

              ctrl.infiniteOrders.numLoaded_ = ctrl.infiniteOrders.toLoad_ - pagination.perPage + data.orders.length;
              ctrl.orders = ctrl.orders.concat(data.orders);

              pagination.searchFailed = !ctrl.orders.length;
              pagination.end = ctrl.orders.length >= data.total_count;
              pagination.busy = false;

              utils.listLoading.hide();
              utils.moreItemsLoad.hide();
            }
          );

        }
      };
      
      ctrl.getLimitPercent = function () {
        if (!ctrl.limit) return 0;
        
        var val = ctrl.limit.usage / ctrl.limit.limit;
        return val > 1 ? 100 : val * 100;
      };
      
      ctrl.onOrderDelete = function (orderId) {

        getLimit();
        
        _.remove(ctrl.orders, function (order) {
          return order.Order.id == orderId;
        });
        
      };
      
      ctrl.onOrderItemUpdate = function () {
        getLimit();
        pagination.reload();
      };

      /*** private ****/
      function getStatuses() {
        Order.statusesAll().then(function (data) {
          var statuses = _.flatMap(data.statuses, function (status) {
            return status.OrderStatus;
          });

          Store.data('statuses').update(statuses);
        });
      }
      
      function getLimit() {
        Order.customerLimit().then(function (data) {
          ctrl.limit = data.CustomerLimit;
        });
      }
      
      function fetchDeliveryPeriods() {
        Order.fetchDeliveryPeriods().then(function (data) {
          Store.data('DeliveryPeriods').update(data.periods);
        });
      }
      
      function onStatusChange(status) {
        pagination.status_id = status;
        pagination.reload();
      }

      ctrl.downloadCSV = function () {
        ctrl.startTime = moment(ctrl.date, 'YYYY/MM/DD').toDate();
        ctrl.endTime = moment(ctrl.date, 'YYYY-MM-DD').toDate();
          var confirm = $mdDialog.confirm({
            template: utils.downloadCSVOrderTemplate(),

            controller: ['$scope', '$mdDialog', function($scope, $mdDialog) {
              $scope.getStartDate = function(date){
                ctrl.startTime = date;
              };
              $scope.getEndDate = function(date){
                ctrl.endTime = date;
              };
              $scope.download = function () {
                angular.element('.global-loading').show();
                var time_start = moment(moment(ctrl.startTime), 'YYYY-MM-DD').format('YYYY-MM-DD');
                var time_end = moment(moment(ctrl.endTime), 'YYYY-MM-DD').format('YYYY-MM-DD');
                Order.downloadOrderCSV({
                  start: time_start,
                  end: time_end,
                  status_id: pagination.status_id,
                })
                  .then(function (){
                  $mdDialog.cancel();
                  angular.element('.global-loading').hide();
                })
                 .catch(function () {
                   $mdDialog.cancel();
                   angular.element('.global-loading').hide();
                });
              };
            }],

            clickOutsideToClose: true
          });

          $mdDialog.show(confirm);
      };

      ctrl.downloadDataStockCSV = function () {
        utils.listLoading.show();

        Order.downloadDataStock().then(function () {
          utils.listLoading.hide();
        })
        .catch(function () {
          utils.listLoading.hide();
        })
      }

    }

    angular.module('printty')
      
      .component('userOrders', {
        require: {
          parentCtrl: '^^userComponent'
        },
        controller: UserOrdersCtrl,
        controllerAs: 'vm',
        templateUrl: 'views/user/orders.html'
      });

})();