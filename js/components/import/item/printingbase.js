(function () {

    angular.module('printty')
        .component('importItemPrintingbase', {
            controller: PrintingBasesCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/item/printingbase.html'
        });

    function PrintingBasesCtrl(Store, utils, PrintingBase, $timeout) {

        'ngInject';

        var ctrl = this;

        /**** variables ******/

        ctrl.bases = [];

        ctrl.pagination = {
            searchQuery: null,
            lastSearch: null,

            loading: false,
            reload: false,
            empty: false
        };

        ctrl.printingbaseDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        var subscriptions = [];


        /****** methods ******/
        ctrl.$onInit = function () {

            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('reference:create:printingbase-frame').subscribe(function () { ctrl.viewCreate(); })
            );

            getBases();

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            }).on('click', function () {
                this.value = null;
            });

        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function () {
            ctrl.pagination.reload = true;
            ctrl.bases = [];

            getBases();
        };

        ctrl.search = function () {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.reload();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        ctrl.viewDetails = function (base) {
            ctrl.printingbaseDetails.type = 'details';
            ctrl.printingbaseDetails.isShown = true;
            ctrl.printingbaseDetails.active = {
                baseId: base.PrintingBase.id
            };
        };

        ctrl.viewCreate = function () {
            ctrl.printingbaseDetails.type = 'add';
            ctrl.printingbaseDetails.isShown = true;
            ctrl.printingbaseDetails.active = {};
        };

        ctrl.onBaseClose = function () {
            ctrl.printingbaseDetails.isShown = false;
        };

        ctrl.onBaseCreate = function () {
            ctrl.reload();
        };

        ctrl.onBaseUpdate = function (base) {
            var originalBase = _.find(ctrl.bases, function (baseItem) {
                return baseItem.PrintingBase.id == base.id;
            });

            _.extend(originalBase.PrintingBase, base);
        };

        ctrl.onBaseDelete = function (baseId) {
            _.remove(ctrl.bases, function(base) {
                return base.PrintingBase.id == baseId;
            });
        };

        /****** private ******/
        function getBases() {

            if (ctrl.pagination.reload) {
                utils.listLoading.show(false)
            }

            PrintingBase.fetchBases({
                paging_size: -1,
                paging_offset: 0,
                conditions_keywords: ctrl.pagination.searchQuery
            })
                .then(function (data) {
                    ctrl.bases = data.printing_bases.reverse();
                    ctrl.pagination.empty = !ctrl.bases.length;
                    ctrl.pagination.reload = false;

                    utils.listLoading.hide();

                });

        }

        ctrl.CSVupload = function (file) {

            if (!file) {
                $('#fileInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'csv' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            utils.globalLoading.show();

            PrintingBase.uploadCSV(file)
                .then(function () {
                    utils.globalLoading.hide();
                    ctrl.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownload = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords : ctrl.pagination.searchQuery
            };

            PrintingBase.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownloadTemplate = function () {
            utils.globalLoading.show();

            PrintingBase.CSVdownloadTemplate()
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

    }

})();