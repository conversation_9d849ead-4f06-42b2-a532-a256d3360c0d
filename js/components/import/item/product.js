(function () {

    angular.module('printty')
        .component('importItemProduct', {
            controller: ImportItemProductCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/item/product.html'
        });

    function ImportItemProductCtrl(ProductionPrinters, Store, utils, electron, ImportProduct, Product, ProductLinkedCode) {

        "ngInject";

        var ctrl = this;

        utils.appLoaded();

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';

        ctrl.products = [];

        var pagination = {
            init: true,
            perPage: 40,
            type_id: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.products = [];

            ctrl.infiniteProducts.numLoaded_ = 0;
            ctrl.infiniteProducts.toLoad_ = 0;
            ctrl.infiniteProducts.getItemAtIndex(1);
        };

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.productDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infiniteProducts = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.products[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                ImportProduct.fetchProducts(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.products.length,
                        conditions_keywords: pagination.searchQuery,
                        type_id: pagination.type_id
                    }
                ).then(
                    function (data) {
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteProducts.numLoaded_ = ctrl.infiniteProducts.toLoad_ - pagination.perPage + data.products.length;
                        ctrl.products = ctrl.products.concat(data.products);

                        pagination.searchFailed = !ctrl.products.length;
                        pagination.end = ctrl.products.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {

            getProductItems();

            ctrl.infiniteProducts.numLoaded_ = 0;
            ctrl.infiniteProducts.toLoad_ = 0;
            ctrl.infiniteProducts.getItemAtIndex(1);

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            });

            $('#fileInput').on('click', function () {
                this.value = null;
            });

        };

        function getProductItems() {

            Product.types().then(function (data) {
                Store.data('ProductTypes').update(data.types);
            });
            Product.categories().then(function (data) {
                Store.data('ProductCategories').update(data.categories);
            });
            Product.printingBases().then(function (data) {
                Store.data('ProductPrintingBases').update(data.printingBases);
            });
            Product.uvFrames().then(function (data) {
                Store.data('ProductUvFrames').update(data.uvFrames);
            });
            ProductLinkedCode.fetchSources().then(function (data) {
                Store.data('ProductLinkedSources').update(data.sources);
            });
            Product.cpFrames().then(function (data) {
                Store.data('ProductCpFrames').update(data.cpFrames);
            });

        }

        ctrl.viewDetails = function (product) {
            ctrl.productDetails.type = 'details';
            ctrl.productDetails.isShown = true;
            ctrl.productDetails.active = product;
        };

        ctrl.onProductClose = function () {
            ctrl.productDetails.isShown = false;
        };

        ctrl.onProductCreate = function () {
            pagination.reload();
        };

        ctrl.onProductUpdate = function (product) {
            var originalProduct = _.find(ctrl.products, function (productItem) {
                return productItem.Product.id == product.Product.id;
            });

            _.extend(originalProduct.Product, product.Product );

            product.ProductCategory = {
                id : product.Product.category_id,
                title : product.Product.category
            };

            product.ProductType = {
                id : product.Product.type_id,
                title : product.Product.type
            };

            _.extend(originalProduct.ProductCategory, product.ProductCategory );
            _.extend(originalProduct.ProductType, product.ProductType );
        };

        ctrl.onProductDelete = function (productId) {
            _.remove(ctrl.products, function(product) {
                return product.Product.id == productId;
            });
        };

        ctrl.CSVupload = function (file) {

            if (!file) {
                $('#fileInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'csv' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            utils.globalLoading.show();

            ImportProduct.uploadCSV(file)
                .then(function () {
                    utils.globalLoading.hide();
                    pagination.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownload = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords : pagination.searchQuery
            };

            ImportProduct.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownloadBatch = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords : pagination.searchQuery
            };

            ImportProduct.downloadCSVBatch(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownloadTemplate = function () {
            utils.globalLoading.show();

            ImportProduct.CSVdownloadTemplate()
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

    }

})();