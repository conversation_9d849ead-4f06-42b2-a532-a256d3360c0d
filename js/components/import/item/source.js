(function () {

    angular.module('printty')
        .component('importItemSource', {
            controller: ImportItemSourceCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/item/source.html'
        });

    function ImportItemSourceCtrl(Auth, $state, Store, utils, Source, electron) {

        "ngInject";

        var ctrl = this;

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';

        ctrl.sources = [];

        ctrl.pagination = {
            searchQuery: null,
            lastSearch: null,

            loading: false,
            reload: false,
            empty: false
        };

        ctrl.sourceDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        var subscriptions = [];


        /****** methods ******/
        ctrl.$onInit = function () {

            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('reference:create:source').subscribe(function () { ctrl.viewCreate(); })
            );

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            });

            $('#fileInput').on('click', function () {
                this.value = null;
            });


            getSources();

        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function () {
            ctrl.pagination.reload = true;
            ctrl.sources = [];

            getSources();
        };

        ctrl.search = function () {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.reload();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        ctrl.viewDetails = function (source) {
            ctrl.sourceDetails.type = 'details';
            ctrl.sourceDetails.isShown = true;
            ctrl.sourceDetails.active = {
                sourceId: source.ProductLinkedSource.id
            };
        };

        ctrl.viewCreate = function () {
            ctrl.sourceDetails.type = 'add';
            ctrl.sourceDetails.isShown = true;
            ctrl.sourceDetails.active = {};
        };

        ctrl.onSourceClose = function () {
            ctrl.sourceDetails.isShown = false;
        };

        ctrl.onSourceCreate = function () {
            ctrl.reload();
        };

        ctrl.onSourceUpdate = function (source) {
            var originalSource = _.find(ctrl.sources, function (sourceItem) {
                return sourceItem.ProductLinkedSource.id == source.id;
            });

            _.extend(originalSource.ProductLinkedSource, source);
        };

        ctrl.onSourceDelete = function (sourceId) {
            _.remove(ctrl.sources, function(source) {
                return source.ProductLinkedSource.id == sourceId;
            });
        };

        /****** private ******/
        function getSources() {

            if (ctrl.pagination.reload) {
                utils.listLoading.show(false)
            }

            Source.fetchSources({
                conditions_keywords: ctrl.pagination.searchQuery
            })
                .then(function (data) {

                    ctrl.sources = data.sources;
                    ctrl.pagination.empty = !ctrl.sources.length;
                    ctrl.pagination.reload = false;

                    utils.listLoading.hide();

                });

        }

        ctrl.CSVupload = function (file) {

            if (!file) {
                $('#fileInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'csv' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            utils.globalLoading.show();

            Source.uploadCSV(file)
                .then(function () {
                    utils.globalLoading.hide();
                    ctrl.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownload = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords : ctrl.pagination.searchQuery
            };

            Source.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownloadTemplate = function () {
            utils.globalLoading.show();

            Source.downloadCSVTemplate()
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

    }

})();