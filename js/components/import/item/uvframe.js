(function () {

    angular.module('printty')
        .component('importItemUvframe', {
            controller: UvFramesCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/item/uvframe.html'
        });

    function UvFramesCtrl(Store, utils, UvFrames, $timeout) {

        'ngInject';

        var ctrl = this;

        /**** variables ******/

        ctrl.uvs = [];

        ctrl.pagination = {
            searchQuery: null,
            lastSearch: null,

            loading: false,
            reload: false,
            empty: false
        };

        ctrl.uvDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        var subscriptions = [];


        /****** methods ******/
        ctrl.$onInit = function () {

            utils.listLoading.show(true);

            subscriptions.push(
                Store.event('reference:create:uv-frame').subscribe(function () { ctrl.viewCreate(); })
            );

            getFrames();

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            }).on('click', function () {
                this.value = null;
            });

        };

        ctrl.$onDestroy = function () {
            _.forEach(subscriptions, function (subscription) { subscription.unsubscribe(); });
        };

        ctrl.reload = function () {
            ctrl.pagination.reload = true;
            ctrl.uvs = [];

            getFrames();
        };

        ctrl.search = function () {
            if (ctrl.pagination.lastSearch === ctrl.pagination.searchQuery) return;
            ctrl.pagination.lastSearch = ctrl.pagination.searchQuery;
            ctrl.reload();
        };

        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

        ctrl.viewDetails = function (uv) {
            ctrl.uvDetails.type = 'details';
            ctrl.uvDetails.isShown = true;
            ctrl.uvDetails.active = {
                uvId: uv.UVFrame.id
            };
        };

        ctrl.viewCreate = function () {
            ctrl.uvDetails.type = 'add';
            ctrl.uvDetails.isShown = true;
            ctrl.uvDetails.active = {};
        };

        ctrl.onUvFrameClose = function () {
            ctrl.uvDetails.isShown = false;
        };

        ctrl.onUvFrameCreate = function () {
            ctrl.reload();
        };

        ctrl.onUvFrameUpdate = function (uv) {
            var originalUv = _.find(ctrl.uvs, function (uvItem) {
                return uvItem.UVFrame.id == uv.id;
            });

            _.extend(originalUv.UVFrame, uv);
        };

        ctrl.onUvFrameDelete = function (uvId) {
            _.remove(ctrl.uvs, function(uv) {
                return uv.UVFrame.id == uvId;
            });
        };

        /****** private ******/
        function getFrames() {

            if (ctrl.pagination.reload) {
                utils.listLoading.show(false)
            }

            UvFrames.fetchUvs({
                conditions_keywords: ctrl.pagination.searchQuery
            })
                .then(function (data) {
                    ctrl.uvs = data.uv_frames.reverse();
                    ctrl.pagination.empty = !ctrl.uvs.length;
                    ctrl.pagination.reload = false;

                    utils.listLoading.hide();

                });

        }

        ctrl.CSVupload = function (file) {

            if (!file) {
                $('#fileInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'csv' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            utils.globalLoading.show();

            UvFrames.uploadCSV(file)
                .then(function () {
                    utils.globalLoading.hide();
                    ctrl.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownload = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords : ctrl.pagination.searchQuery
            };

            UvFrames.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownloadTemplate = function () {
            utils.globalLoading.show();

            UvFrames.CSVdownloadTemplate()
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

    }

})();