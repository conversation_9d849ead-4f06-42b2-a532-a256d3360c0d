(function () {

    angular.module('printty')
        .component('importOtherPlate', {
            controller: ImportOtherPlateCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/plate.html'
        });

    function ImportOtherPlateCtrl(Auth, $state, Store, utils, ProductPlate, electron) {

        "ngInject";

        var ctrl = this;

        utils.appLoaded();

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';

        ctrl.plates = [];

        var pagination = {
            init: true,
            perPage: 40,
            type_id: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.plates = [];

            ctrl.infinitePlates.numLoaded_ = 0;
            ctrl.infinitePlates.toLoad_ = 0;
            ctrl.infinitePlates.getItemAtIndex(1);
        };

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.plateDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infinitePlates = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.plates[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                ProductPlate.fetchPlates(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.plates.length,
                        conditions_keywords: pagination.searchQuery,
                        type_id: pagination.type_id
                    }
                ).then(
                    function (data) {
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infinitePlates.numLoaded_ = ctrl.infinitePlates.toLoad_ - pagination.perPage + data.plates.length;
                        ctrl.plates = ctrl.plates.concat(data.plates);

                        pagination.searchFailed = !ctrl.plates.length;
                        pagination.end = ctrl.plates.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {

            ctrl.infinitePlates.numLoaded_ = 0;
            ctrl.infinitePlates.toLoad_ = 0;
            ctrl.infinitePlates.getItemAtIndex(1);

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            });

            $('#fileInput').on('click', function () {
                this.value = null;
            });

        };

        ctrl.viewDetails = function (plate) {
            ctrl.plateDetails.type = 'details';
            ctrl.plateDetails.isShown = true;
            ctrl.plateDetails.active = {
                plateId: plate.ProductColorSideSizeLinkedPlate.id
            };
        };

        ctrl.onPlateClose = function () {
            ctrl.plateDetails.isShown = false;
        };

        ctrl.onPlateCreate = function () {
            pagination.reload();
        };

        ctrl.onPlateUpdate = function (plate) {
            var originalPlate = _.find(ctrl.plates, function (plateItem) {
                return plateItem.ProductColorSideSizeLinkedPlate.id == plate.id;
            });

            _.extend(originalPlate.ProductColorSideSizeLinkedPlate, plate);
            _.extend(originalPlate.PlateJoin, plate);
        };

        ctrl.onPlateDelete = function (plateId) {
            _.remove(ctrl.plates, function(plate) {
                return plate.ProductColorSideSizeLinkedPlate.id == plateId;
            });
        };

        ctrl.CSVupload = function (file) {

            if (!file) {
                $('#fileInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'csv' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            utils.globalLoading.show();

            ProductPlate.uploadCSV(file)
                .then(function () {
                    utils.globalLoading.hide();
                    pagination.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownload = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords: pagination.searchQuery
            };

            ProductPlate.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVTemplateDownload = function () {
            utils.globalLoading.show();

            ProductPlate.downloadCSVTemplate()
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

    }

})();