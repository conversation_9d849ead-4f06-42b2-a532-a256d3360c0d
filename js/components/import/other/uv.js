(function () {

    angular.module('printty')
        .component('importOtherUv', {
            controller: ImportOtherUvCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/uv.html'
        });

    function ImportOtherUvCtrl(Auth, $state, Store, utils, Uv, electron) {

        "ngInject";

        var ctrl = this;

        utils.appLoaded();

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';

        ctrl.uvs = [];

        var pagination = {
            init: true,
            perPage: 40,
            type_id: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.uvs = [];

            ctrl.infiniteUvs.numLoaded_ = 0;
            ctrl.infiniteUvs.toLoad_ = 0;
            ctrl.infiniteUvs.getItemAtIndex(1);
        };

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.uvDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infiniteUvs = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.uvs[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                Uv.fetchUvs(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.uvs.length,
                        conditions_keywords: pagination.searchQuery,
                        type_id: pagination.type_id
                    }
                ).then(
                    function (data) {
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteUvs.numLoaded_ = ctrl.infiniteUvs.toLoad_ - pagination.perPage + data.uvs.length;
                        ctrl.uvs = ctrl.uvs.concat(data.uvs);

                        pagination.searchFailed = !ctrl.uvs.length;
                        pagination.end = ctrl.uvs.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {

            ctrl.infiniteUvs.numLoaded_ = 0;
            ctrl.infiniteUvs.toLoad_ = 0;
            ctrl.infiniteUvs.getItemAtIndex(1);

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            });

            $('#fileInput').on('click', function () {
                this.value = null;
            });

        };

        ctrl.viewDetails = function (uv) {
            ctrl.uvDetails.type = 'details';
            ctrl.uvDetails.isShown = true;
            ctrl.uvDetails.active = {
                uvId: uv.ProductUVOption.id
            };
        };

        ctrl.onUvClose = function () {
            ctrl.uvDetails.isShown = false;
        };

        ctrl.onUvCreate = function () {
            pagination.reload();
        };

        ctrl.onUvUpdate = function (uv) {
            var originalUv = _.find(ctrl.uvs, function (uvItem) {
                return uvItem.ProductUVOption.id == uv.id;
            });

            _.extend(originalUv.ProductUVOption, uv);
        };

        ctrl.onUvDelete = function (uvId) {
            _.remove(ctrl.uvs, function(uv) {
                return uv.ProductUVOption.id == uvId;
            });
        };

        ctrl.CSVupload = function (file) {

            if (!file) {
                $('#fileInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'csv' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            utils.globalLoading.show();

            Uv.uploadCSV(file)
                .then(function () {
                    utils.globalLoading.hide();
                    pagination.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownload = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords : pagination.searchQuery
            };

            Uv.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVTemplateDownload = function () {
            utils.globalLoading.show();

            Uv.downloadCSVTemplate()
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

    }

})();