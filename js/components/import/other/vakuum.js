(function () {

    angular.module('printty')
        .component('importOtherVakuum', {
            controller: ImportOtherVakuumCtrl,
            controllerAs: 'vm',
            templateUrl: 'views/import/other/vakuum.html'
        });

    function ImportOtherVakuumCtrl(Auth, $state, Store, utils, Vakuum, electron) {

        "ngInject";

        var ctrl = this;

        utils.appLoaded();

        /**** variables ******/

        ctrl.isElectron = !!electron;
        ctrl.uploadFile = '';

        ctrl.vakuums = [];

        var pagination = {
            init: true,
            perPage: 40,
            type_id: null,

            searching: false,
            searchQuery: '',
            lastSearch: '',
            searchFailed: false,

            busy: false,
            end: false
        };
        ctrl.pagination = pagination;

        pagination.reload = function () {

            this.init = true;
            this.end = false;
            this.busy = false;

            ctrl.vakuums = [];

            ctrl.infiniteVakuums.numLoaded_ = 0;
            ctrl.infiniteVakuums.toLoad_ = 0;
            ctrl.infiniteVakuums.getItemAtIndex(1);
        };

        /** Search block **/
        ctrl.liveSearch = function () {
            utils.delay(function() {
                ctrl.search();
            }, 350 );
        };

        ctrl.search = function () {
            if (pagination.lastSearch === pagination.searchQuery) return;

            pagination.lastSearch = pagination.searchQuery;
            pagination.searching = true;
            pagination.reload();
        };

        ctrl.clearSearch = function () {
            pagination.searchQuery = '';
            pagination.search();
        };

        ctrl.vakuumDetails = {
            isShown: false,
            type: 'add',
            active: {}
        };

        ctrl.infiniteVakuums = {
            numLoaded_: 0,
            toLoad_: 0,
            getItemAtIndex: function(index) {

                if (index > this.numLoaded_) {
                    this.fetchMoreItems_(index);
                    return null;
                }

                return ctrl.vakuums[index];

            },
            getLength: function() {
                return this.numLoaded_ + 3;
            },
            fetchMoreItems_: function(index) {

                if (this.toLoad_ >= index || pagination.end || pagination.busy) return;

                pagination.busy = true;

                if (pagination.init) {
                    utils.listLoading.show(!pagination.searching);
                } else {
                    utils.moreItemsLoad.show();
                }

                this.toLoad_ += pagination.perPage;

                Vakuum.fetchVakuums(
                    {
                        paging_size: pagination.perPage,
                        paging_offset: ctrl.vakuums.length,
                        conditions_keywords: pagination.searchQuery,
                        type_id: pagination.type_id
                    }
                ).then(
                    function (data) {
                        pagination.init = false;
                        pagination.searching = false;

                        ctrl.infiniteVakuums.numLoaded_ = ctrl.infiniteVakuums.toLoad_ - pagination.perPage + data.vakuums.length;
                        ctrl.vakuums = ctrl.vakuums.concat(data.vakuums);

                        pagination.searchFailed = !ctrl.vakuums.length;
                        pagination.end = ctrl.vakuums.length >= data.total_count;
                        pagination.busy = false;

                        utils.listLoading.hide();
                        utils.moreItemsLoad.hide();
                    }
                );

            }
        };

        /****** methods ******/
        ctrl.$onInit = function () {

            ctrl.infiniteVakuums.numLoaded_ = 0;
            ctrl.infiniteVakuums.toLoad_ = 0;
            ctrl.infiniteVakuums.getItemAtIndex(1);

            $('#fileInput').on('change', function (e) {
                ctrl.CSVupload(e.target.files[0]);
            });

            $('#fileInput').on('click', function () {
                this.value = null;
            });

        };

        ctrl.viewDetails = function (vakuum) {
            ctrl.vakuumDetails.type = 'details';
            ctrl.vakuumDetails.isShown = true;
            ctrl.vakuumDetails.active = {
                vakuumId: vakuum.ProductVakuumOption.id
            };
        };

        ctrl.onVakuumClose = function () {
            ctrl.vakuumDetails.isShown = false;
        };

        ctrl.onVakuumCreate = function () {
            pagination.reload();
        };

        ctrl.onVakuumUpdate = function (vakuum) {
            var originalVakuum = _.find(ctrl.vakuums, function (vakuumItem) {
                return vakuumItem.ProductVakuumOption.id == vakuum.id;
            });

            _.extend(originalVakuum.ProductVakuumOption, vakuum);
        };

        ctrl.onVakuumDelete = function (vakuumId) {
            _.remove(ctrl.vakuums, function(vakuum) {
                return vakuum.ProductVakuumOption.id == vakuumId;
            });
        };

        ctrl.CSVupload = function (file) {

            if (!file) {
                $('#fileInput').click();
                return;
            }

            var fileType = file.name.slice((Math.max(0, file.name.lastIndexOf(".")) || Infinity) + 1);

            if ( fileType !== 'csv' ) {
                alert('File.UnexpectedFormat');
                return;
            }

            utils.globalLoading.show();

            Vakuum.uploadCSV(file)
                .then(function () {
                    utils.globalLoading.hide();
                    pagination.reload();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVdownload = function () {
            utils.globalLoading.show();

            var params = {
                conditions_keywords : pagination.searchQuery
            };

            Vakuum.downloadCSV(params)
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.CSVTemplateDownload = function () {
            utils.globalLoading.show();

            Vakuum.downloadCSVTemplate()
                .then(function () {
                    utils.globalLoading.hide();
                })
                .catch(function () {
                    utils.globalLoading.hide();
                });

        };

        ctrl.clearSearch = function () {
            ctrl.pagination.searchQuery = '';
            ctrl.search();
        };

    }

})();