(function () {
  angular.module('printty')

    .config(['$stateProvider', '$urlRouterProvider', function ($stateProvider, $urlRouterProvider) {

      // update session if available
      var checkSession = ['$q', 'Auth', function ($q, Auth) {
        var deferred = $q.defer();

        if ( Auth.session.get() ) {

          Auth.update().then(
            function() {
              deferred.resolve(angular.noop);
            },
            function() {
              Auth.signout().finally(function () {
                deferred.reject('Session.NotFound');
              });
            }
          );

        } else {
          deferred.resolve();
        }

        return deferred.promise;
      }];

      // redirect to auth.signin if not authenticated
      var ifAuthenticated = ['Auth', '$q', '$timeout', '$state', function (Auth, $q, $timeout, $state) {

        var deferred = $q.defer();

        if (Auth.isAuthenticated()) {
          deferred.resolve();
        } else {
          $timeout(function () {
            $state.go('auth.signin');
            deferred.resolve();
          }, 0);
        }

        return deferred.promise;

      }];

      $stateProvider

        .state('root', {
          abstract: true,
          resolve: {
            checkSession: checkSession
          },
          template: '<printty-app class="root" ui-view></printty-app>'
        })

        // auth
        .state('auth', {
          abstract: true,
          parent: 'root',
          template: '<login-component></login-component>',
          onEnter: function() {
            angular.element('body').addClass('login-page');
          },
          onExit: function() {
            angular.element('body').removeClass('login-page');
          }
        })
        .state('auth.signin', {
          url: '/signin',
          templateUrl: 'views/login/sign-in.html'
        })
        .state('auth.signup', {
          url: '/signup',
          templateUrl: 'views/login/sign-up.html'
        })
        .state('auth.forgot-pass', {
          url: '/forgot-pass',
          templateUrl: 'views/login/forgot-pass.html'
        })

        // user
        .state('user', {
          parent: 'root',
          template: '<user-component></user-component>',
          url: '/user',
          redirectTo: 'user.orders',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })
        .state('user.orders', {
          url: '/orders',
          template: '<user-orders class="block-wide-scroll scroll-no-jump component"></user-orders>',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })
        // .state('user.history', {
        //   url: '/history',
        //   template: '<user-history class="block-wide-scroll scroll-no-jump component"></user-history>',
        //   resolve: {
        //     ifAuthenticated: ifAuthenticated
        //   }
        // })

          .state('user.billing', {
              url: '/billing',
              template: '<user-billing class="block-wide-scroll scroll-no-jump component"></user-billing>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('user.blank', {
              url: '/blank',
              template: '<user-blank class="block-wide-scroll scroll-no-jump component"></user-blank>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

        // Admin
        .state('admin', {
          parent: 'root',
          template: '<admin-component></admin-component>',
          url: '/admin',
          redirectTo: 'admin.income',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })

        .state('admin.global-search', {
          template: '<admin-global-search class="block-wide-scroll scroll-no-jump component"></admin-global-search>',
          url: '/search?q',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })

        // Admin - Income
        .state('admin.income', {
          template: '<admin-income class="absolute-wide component"></admin-income>',
          url: '/income',
          resolve: {
            ifAuthenticated: ifAuthenticated
          },
          redirectTo: 'admin.income.clients'
        })
        .state('admin.income.clients', {
          template: '<admin-income-clients class="block-wide-scroll scroll-no-jump component"></admin-income-clients>',
          url: '/clients',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })
        // .state('admin.income.companies', {
        //   template: '<admin-income-companies class="block-wide-scroll scroll-no-jump component"></admin-income-companies>',
        //   url: '/companies',
        //   resolve: {
        //     ifAuthenticated: ifAuthenticated
        //   }
        // })

        // Admin - Plan
        .state('admin.production', {
          template: '<admin-production class="absolute-wide component"></admin-production>',
          url: '/production',
          redirectTo: 'admin.production.printers',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })
        .state('admin.production.tasks', {
          template: '<admin-production-tasks class="block-wide-scroll scroll-no-jump component"></admin-production-tasks>',
          url: '/tasks?q',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })

        .state('admin.production.places', {
          template: '<admin-production-places class="block-wide-scroll scroll-no-jump component"></admin-production-places>',
          url: '/places?q',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })

        .state('admin.production.waiting', {
          template: '<admin-production-waiting class="block-wide-scroll scroll-no-jump component"></admin-production-waiting>',
          url: '/waiting?q',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })
        .state('admin.production.printers', {
          template: '<admin-production-printers class="block-wide-scroll scroll-no-jump component"></admin-production-printers>',
          url: '/printers?q',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })

        // Admin - Users
        .state('admin.users', {
          url: '/users',
          template: '<admin-users class="block-wide-scroll scroll-no-jump component"></admin-users>',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })

        // Admin - Error log
        .state('admin.error-log', {
          url: '/error-log',
          template: '<admin-error-log class="block-wide-scroll scroll-no-jump component"></admin-error-log>',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })

          // admin - body order
         .state('admin.body-order', {
            url: '/body-order',
            template: '<admin-body-order class="block-wide-scroll scroll-no-jump component"></admin-body-order>',
            resolve: {
                ifAuthenticated: ifAuthenticated
            }
          })

          .state('admin.image-error', {
              url: '/image-error',
              template: '<admin-image-error class="block-wide-scroll scroll-no-jump component"></admin-image-error>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('admin.order-status-search', {
              url: '/order-status-search',
              template: '<admin-order-status-search class="block-wide-scroll scroll-no-jump component"></admin-order-status-search>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('admin.blank', {
              url: '/blank',
              template: '<admin-blank class="block-wide-scroll scroll-no-jump component"></admin-blank>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          // Dashboard
        // .state('dashboard', {
        //   parent: 'root',
        //   template: '<dashboard-component></dashboard-component>',
        //   url: '/dashboard',
        //   resolve: {
        //     ifAuthenticated: ifAuthenticated
        //   }
        // })

        // Printing
        .state('print', {
          parent: 'root',
          template: '<print-component class="block-wide-scroll component"></print-component>',
          url: '/print',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })

          // dtftranscription
          .state('dtftranscription', {
              parent: 'root',
              template: '<dtf-transcription-component class="block-wide-scroll component"></dtf-transcription-component>',
              url: '/dtf-transcription',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

      // Printing original
      .state('printoriginal', {
          parent: 'root',
          template: '<print-original-component class="block-wide-scroll component"></print-original-component>',
          url: '/print-original',
          resolve: {
              ifAuthenticated: ifAuthenticated
          }
      })

        // Delivery
        .state('delivery', {
          parent: 'root',
          template: '<delivery-component class="block-wide-scroll component"></delivery-component>',
          url: '/delivery',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })

        // Storage
        .state('storage', {
          parent: 'root',
          template: '<storage-component></storage-component>',
          url: '/storage',
          resolve: {
            ifAuthenticated: ifAuthenticated
          },
          redirectTo: 'storage.current'
        })
        .state('storage.current', {
          url: '/current',
          template: '<storage-current class="block-wide-scroll scroll-no-jump component"></storage-current>',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })
        .state('storage.activity', {
          url: '/activity',
          template: '<storage-activity class="block-wide-scroll scroll-no-jump component"></storage-activity>',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })
        .state('storage.supplier', {
            url: '/supplier',
            template: '<storage-supplier class="block-wide-scroll scroll-no-jump component"></storage-supplier>',
            resolve: {
                ifAuthenticated: ifAuthenticated
            }
        })
        .state('storage.item', {
            url: '/item',
            template: '<storage-item class="block-wide-scroll scroll-no-jump component"></storage-item>',
            resolve: {
                ifAuthenticated: ifAuthenticated
            }
        })
        .state('storage.optionitems', {
          url: '/option-items',
          template: '<storage-option-items class="block-wide-scroll scroll-no-jump component"></storage-option-items>',
          resolve: {
              ifAuthenticated: ifAuthenticated
          }
        })
        .state('storage.optionitems_activity', {
          url: '/option-items-activity',
          template: '<storage-option-items-activity class="block-wide-scroll scroll-no-jump component"></storage-option-items-activity>',
          resolve: {
              ifAuthenticated: ifAuthenticated
          }
        })

        // Kenpin
          .state('kenpin', {
              parent: 'root',
              template: '<kenpin-component class="block-wide-scroll component"></kenpin-component>',
              url: '/kenpin',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

        // KenpinI
          .state('kenpini', {
              parent: 'root',
              template: '<kenpini-component class="block-wide-scroll component"></kenpini-component>',
              url: '/kenpini',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          // Kenpin
          .state('kenpinuv', {
              parent: 'root',
              template: '<kenpinuv-component class="block-wide-scroll component"></kenpinuv-component>',
              url: '/kenpinuv',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          // Random
          .state('random', {
              parent: 'root',
              template: '<random-component class="block-wide-scroll component"></random-component>',
              url: '/random',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          // New Random
          .state('newrandom', {
              parent: 'root',
              template: '<newrandom-component class="block-wide-scroll component"></newrandom-component>',
              url: '/newrandom',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          // Andonkai
          .state('andonkai', {
              parent: 'root',
              template: '<andonkai-component class="block-wide-scroll component"></andonkai-component>',
              url: '/andonkai',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          // Seiren Random
          .state('seirenrandom', {
              parent: 'root',
              template: '<seirenrandom-component class="block-wide-scroll component"></seirenrandom-component>',
              url: '/seirenrandom',
               resolve: {
                  ifAuthenticated: ifAuthenticated
               }
          })

          .state('totalrandom', {
              parent: 'root',
              template: '<totalrandom-component class="block-wide-scroll component"></totalrandom-component>',
              url: '/totalrandom',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          // Random uv
          .state('randomuv', {
              parent: 'root',
              template: '<randomuv-component class="block-wide-scroll component"></randomuv-component>',
              url: '/randomuv',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('andonstatistical', {
              parent: 'root',
              template: '<andonstatistical-component class="block-wide-scroll component"></andonstatistical-component>',
              url: '/andon-statistical',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

        // Reference
        .state('reference', {
          parent: 'root',
          template: '<reference-component></reference-component>',
          url: '/reference',
          resolve: {
            ifAuthenticated: ifAuthenticated
          },
          redirectTo: 'reference.printers'
        })
        .state('reference.printers', {
          url: '/printers',
          template: '<reference-printers class="block-wide-scroll scroll-no-jump component"></reference-printers>',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })
        .state('reference.products', {
          url: '/products',
          template: '<reference-products class="block-wide-scroll scroll-no-jump component"></reference-products>',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })
        .state('reference.plates', {
          url: '/plates',
          template: '<reference-plates class="block-wide-scroll scroll-no-jump component"></reference-plates>',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })
        .state('reference.places', {
          url: '/places',
          template: '<reference-places class="block-wide-scroll scroll-no-jump component"></reference-places>',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })

          .state('reference.places-seiren', {
              url: '/places-seiren',
              template: '<reference-places-seiren class="block-wide-scroll scroll-no-jump component"></reference-places-seiren>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('reference.second-factories', {
              url: '/second-factories',
              template: '<reference-second-factories class="block-wide-scroll scroll-no-jump component"></reference-second-factories>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('reference.sources', {
              url: '/sources',
              template: '<reference-sources class="block-wide-scroll scroll-no-jump component"></reference-sources>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('reference.reasons', {
              url: '/reasons',
              template: '<reference-reasons class="block-wide-scroll scroll-no-jump component"></reference-reasons>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
        .state('reference.printing-bases', {
          url: '/printing-bases',
          template: '<reference-printing-bases class="block-wide-scroll scroll-no-jump component"></reference-printing-bases>',
          resolve: {
            ifAuthenticated: ifAuthenticated
          }
        })
          .state('reference.uv-frames', {
              url: '/uv-frames',
              template: '<reference-uv-frames class="block-wide-scroll scroll-no-jump component"></reference-uv-frames>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('reference.cp-frames', {
              url: '/cp-frames',
              template: '<reference-cp-frames class="block-wide-scroll scroll-no-jump component"></reference-cp-frames>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('reference.clients', {
              url: '/clients',
              template: '<reference-clients class="block-wide-scroll scroll-no-jump component"></reference-clients>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('reference.clients-kornit', {
              url: '/clients-kornit',
              template: '<reference-clients-kornit class="block-wide-scroll scroll-no-jump component"></reference-clients-kornit>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('reference.trims', {
              url: '/trims',
              template: '<reference-trims class="block-wide-scroll scroll-no-jump component"></reference-trims>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('reference.warehouses', {
              url: '/warehouses',
              template: '<reference-warehouses class="block-wide-scroll scroll-no-jump component"></reference-warehouses>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('reference.categories', {
              url: '/categories',
              template: '<reference-categories class="block-wide-scroll scroll-no-jump component"></reference-categories>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('reference.image-name', {
              url: '/image-name',
              template: '<reference-image-name class="block-wide-scroll scroll-no-jump component"></reference-image-name>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('reference.order-product-code', {
              url: '/order-product-code',
              template: '<reference-order-product-code class="block-wide-scroll scroll-no-jump component"></reference-order-product-code>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('reference.price-csv', {
              url: '/price-csv',
              template: '<reference-price-csv class="block-wide-scroll scroll-no-jump component"></reference-price-csv>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('reference.product-cell', {
              url: '/product-cell',
              template: '<reference-product-cell class="block-wide-scroll scroll-no-jump component"></reference-product-cell>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('reference.andon', {
              url: '/andon',
              template: '<reference-andon class="block-wide-scroll scroll-no-jump component"></reference-andon>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('reference.factories', {
              url: '/factories',
              template: '<reference-factories class="block-wide-scroll scroll-no-jump component"></reference-factories>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('reference.optionitems', {
              url: '/option-items',
              template: '<reference-option-items class="block-wide-scroll scroll-no-jump component"></reference-option-items>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          // manage image
          .state('image', {
              parent: 'root',
              template: '<image-component></image-component>',
              url: '/image',
              redirectTo: 'image.single',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('image.single', {
              template: '<image-single class="absolute-wide scroll-no-jump component"></image-single>',
              url: '/single',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('image.group', {
              template: '<image-group class="absolute-wide component"></image-group>',
              url: '/group',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('image.delivery', {
              template: '<image-delivery class="absolute-wide component"></image-delivery>',
              url: '/delivery',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('image.qrgroup', {
              template: '<image-qrgroup class="absolute-wide component"></image-qrgroup>',
              url: '/qrgroup',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('image.uv', {
              template: '<image-uv class="absolute-wide component"></image-uv>',
              url: '/uv',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('image.sublimation', {
              template: '<image-sublimation class="absolute-wide component"></image-sublimation>',
              url: '/sublimation',
              redirectTo: 'image.sublimation.noto',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('image.sublimation.noto', {
              template: '<image-sublimation-noto class="block-wide-scroll scroll-no-jump component"></image-sublimation-noto>',
              url: '',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('image.dtf', {
              template: '<image-dtf class="absolute-wide component"></image-dtf>',
              url: '/dtf',
              redirectTo: 'image.dtf.noto',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('image.dtf.noto', {
              template: '<image-dtf-noto class="block-wide-scroll scroll-no-jump component"></image-dtf-noto>',
              url: '',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('image.paper_print', {
              template: '<image-paper-print class="absolute-wide component"></image-paper-print>',
              url: '/paper-print',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('image.material', {
              template: '<image-material class="absolute-wide component"></image-material>',
              url: '/material',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('image.textile', {
              template: '<image-textile class="absolute-wide component"></image-textile>',
              url: '/textile',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('image.data_3d', {
              template: '<image-data-3d class="absolute-wide component"></image-data-3d>',
              url: '/data-3d',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('image.external_factory_print_data', {
              template: '<image-external-factory-print-data class="absolute-wide component"></image-external-factory-print-data>',
              url: '/external-factory-print-data',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          // add item
          .state('import', {
              parent: 'root',
              template: '<import-component></import-component>',
              url: '/import',
              redirectTo: 'import.item.product',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.item', {
              template: '<import-item class="absolute-wide component"></import-item>',
              url: '/item',
              redirectTo: 'import.item.product',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.item.product', {
              template: '<import-item-product class="block-wide-scroll scroll-no-jump component"></import-item-product>',
              url: '/product',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.item.source', {
              template: '<import-item-source class="block-wide-scroll scroll-no-jump component"></import-item-source>',
              url: '/source',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.item.uvframe', {
              template: '<import-item-uvframe class="block-wide-scroll scroll-no-jump component"></import-item-uvframe>',
              url: '/uvframe',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.item.cpframe', {
              template: '<import-item-cpframe class="block-wide-scroll scroll-no-jump component"></import-item-cpframe>',
              url: '/cpframe',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.item.printingbase', {
              template: '<import-item-printingbase class="block-wide-scroll scroll-no-jump component"></import-item-printingbase>',
              url: '/printingbase',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.other', {
              template: '<import-other class="absolute-wide component"></import-other>',
              url: '/other',
              redirectTo: 'import.other.color',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.other.color', {
              template: '<import-other-color class="block-wide-scroll scroll-no-jump component"></import-other-color>',
              url: '/color',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.other.size', {
              template: '<import-other-size class="block-wide-scroll scroll-no-jump component"></import-other-size>',
              url: '/size',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.other.uv', {
              template: '<import-other-uv class="block-wide-scroll scroll-no-jump component"></import-other-uv>',
              url: '/uv',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.other.vakuum', {
              template: '<import-other-vakuum class="block-wide-scroll scroll-no-jump component"></import-other-vakuum>',
              url: '/vakuum',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.other.cp', {
              template: '<import-other-cp class="block-wide-scroll scroll-no-jump component"></import-other-cp>',
              url: '/cp',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.other.side', {
              template: '<import-other-side class="block-wide-scroll scroll-no-jump component"></import-other-side>',
              url: '/side',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.other.plate', {
              template: '<import-other-plate class="block-wide-scroll scroll-no-jump component"></import-other-plate>',
              url: '/plate',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })
          .state('import.other.optionitem', {
              template: '<import-other-option-item class="block-wide-scroll scroll-no-jump component"></import-other-option-item>',
              url: '/option-item',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          // quality
          .state('quality', {
              parent: 'root',
              template: '<quality-component></quality-component>',
              url: '/quality',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              },
              redirectTo: 'quality.list'
          })
          .state('quality.list', {
              url: '/list',
              template: '<quality-list class="block-wide-scroll scroll-no-jump component"></quality-list>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('repair', {
              parent: 'root',
              template: '<repair-component class="block-wide-scroll component"></repair-component>',
              url: '/repair',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

        // business project
          .state('business', {
               parent: 'root',
               url: '/business',
               template: '<business-component class="block-wide-scroll scroll-no-jump component"></business-component>',
               resolve: {
               ifAuthenticated: ifAuthenticated
            }
          })

        // barcode
        .state('barcode', {
            parent: 'root',
            url: '/barcode',
            template: '<barcode-component class="block-wide-scroll scroll-no-jump component"></barcode-component>',
            resolve: {
                ifAuthenticated: ifAuthenticated
            }
        })

          // factory-order
          .state('factoryorder', {
              parent: 'root',
              template: '<factoryorder-component></factoryorder-component>',
              url: '/factory-order',
              redirectTo: 'factoryorder.factory',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('factoryorder.factory', {
              template: '<factory-component class="absolute-wide scroll-no-jump component"></factory-component >',
              url: '/factory',
              resolve: {
                  ifAuthnticated: ifAuthenticated
              }
            })

          // bulk bulk-shipping
          .state('shipping', {
              parent: 'root',
              template: '<shipping-component></shipping-component>',
              url: '/shipping',
              redirectTo: 'shipping.bulk',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          })

          .state('shipping.bulk', {
              url: '/bulk',
              template: '<shipping-bulk-component class="block-wide-scroll scroll-no-jump component"></shipping-bulk-component>',
              resolve: {
                  ifAuthenticated: ifAuthenticated
              }
          });

      $urlRouterProvider.otherwise('/signin');

    }]);
})();

