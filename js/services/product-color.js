(function () {

  angular.module('printty')

    .factory('ProductColor', function (ApiService, $q, Auth) {

      "ngInject";

      var API_MAP = {
        colors: '/products/product/colors',
        color: '/products/product/colors/color',
        setColorMain: '/products/product/colors/color/main/set',

        sides: '/products/product/colors/color/sides',
        side: '/products/product/colors/color/sides/side',
        setSideMain: '/products/product/colors/color/sides/side/main/set',
        
        sideLinkedPlates: '/products/product/colors/color/sides/side/linked/plates',
        sideLinkedPlate: '/products/product/colors/color/sides/side/linked/plates/plate',

        codes: '/products/product/colors/color/linked/codes',
        code: '/products/product/colors/color/linked/codes/code'
      };

      return {
        
        list: colorList,
        details: colorDetails,
        create: colorCreate,
        update: colorUpdate,
        delete: colorDelete,
        
        setAsMain: colorSetAsMain,

        side: {
          list: colorSideList,
          details: colorSideDetails,
          create: colorSideCreate,
          update: colorSideUpdate,
          delete: colorSideDelete,
          
          setAsMain: colorSideSetAsMain,
          
          linkedPlates: {
            fetchPlates: fetchColorSideLinkedPlates,
            fetchPlate: fetchColorSideLinkedPlate,
            createPlate: createColorSideLinkedPlate,
            updatePlate: updateColorSideLinkedPlate,
            deletePlate: deleteColorSideLinkedPlate
          }
        },

        linkCode: {
          fetchCodes: fetchCodes,

          fetchCode: fetchCode,
          createCode: createCode,
          updateCode: updateCode,
          deleteCode: deleteCode
        }
      };
      
      function colorList(productId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          product_id: productId
        };

        ApiService.get(API_MAP.colors, params).then(
          function(data) {

            _.forEach(data.colors, function (color) {
              color.ProductColor.is_main = ApiService.toBoolean(color.ProductColor.is_main);
            });

            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function colorDetails(colorId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          color_id: colorId
        };

        ApiService.get(API_MAP.color, params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function colorCreate(colorData) {
        var deferred = $q.defer();

        var postData  = angular.merge(
          { session: Auth.session.get() },
          colorData
        );

        ApiService.put(API_MAP.color, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function colorUpdate(colorData) {
        var deferred = $q.defer();

        var postData  = angular.merge(
          { session: Auth.session.get() },
          colorData
        );

        ApiService.post(API_MAP.color, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function colorDelete(colorId) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          ProductColor: {
            id: colorId
          }
        };

        ApiService.delete(API_MAP.color, data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function colorSetAsMain(colorId) {
        var deferred = $q.defer();

        var postData = {
          session: Auth.session.get(),
          ProductColor: {
            id: colorId
          }
        };

        ApiService.post(API_MAP.setColorMain, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      /*** Color sides ****/
      function colorSideList(colorId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          color_id: colorId
        };

        ApiService.get(API_MAP.sides, params).then(
          function(data) {

            _.forEach(data.sides, function (side) {
              side.ProductColorSide.is_main = ApiService.toBoolean(side.ProductColorSide.is_main);
            });

            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function colorSideDetails(sideId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          side_id: sideId
        };

        ApiService.get(API_MAP.side, params).then(
          function(data) {
            data.ProductColorSide.content = JSON.stringify( JSON.parse(data.ProductColorSide.content), null, ' ' );
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function colorSideCreate(colorSideData) {
        var deferred = $q.defer();

        var postData  = angular.merge(
          { session: Auth.session.get() },
          colorSideData
        );

        ApiService.put(API_MAP.side, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function colorSideUpdate(colorSideData) {
        var deferred = $q.defer();

        var postData  = angular.merge(
          { session: Auth.session.get() },
          colorSideData
        );

        ApiService.post(API_MAP.side, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function colorSideDelete(colorSideId) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          ProductColorSide: {
            id: colorSideId
          }
        };

        ApiService.delete(API_MAP.side, data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function colorSideSetAsMain(colorSideId) {
        var deferred = $q.defer();

        var postData = {
          session: Auth.session.get(),
          ProductColorSide: {
            id: colorSideId
          }
        };

        ApiService.post(API_MAP.setSideMain, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      /*** color linked plates ******/
      function fetchColorSideLinkedPlates(sideId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          side_id: sideId
        };

        ApiService.get(API_MAP.sideLinkedPlates, params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchColorSideLinkedPlate(plateId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          plate_id: plateId
        };

        ApiService.get(API_MAP.sideLinkedPlate, params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function createColorSideLinkedPlate(params) {
        var deferred = $q.defer();

        var putData = {
          session: Auth.session.get(),
          ProductColorSide: {
            id: params.sideId
          },
          ProductSize: {
            id: params.sizeId
          },
          Plate: {
            id: params.plateId
          }
        };

        ApiService.put(API_MAP.sideLinkedPlate, putData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function updateColorSideLinkedPlate(plateData) {
        var deferred = $q.defer();

        var postData  = {
          session: Auth.session.get(),
          ProductColorSideSizeLinkedPlate: {
            id: plateData.id
          },
          ProductSize: {
            id: plateData.size_id
          },
          Plate: {
            id: plateData.plate_id
          }
        };

        ApiService.post(API_MAP.sideLinkedPlate, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function deleteColorSideLinkedPlate(plateId) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          ProductColorSideSizeLinkedPlate: {
            id: plateId
          }
        };

        ApiService.delete(API_MAP.sideLinkedPlate, data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      /*** linked codes ***/

      function fetchCodes(colorId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          color_id: colorId
        };

        ApiService.get(API_MAP.codes, params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchCode(codeId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          code_id: codeId
        };

        ApiService.get(API_MAP.code, params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function createCode(colorId, codeData) {
        var deferred = $q.defer();

        var postData  = {
          session: Auth.session.get(),
          ProductColor: {
            id: colorId
          },
          ProductColorLinkedCode: codeData
        };

        ApiService.put(API_MAP.code, postData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function updateCode(codeData) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          ProductColorLinkedCode: codeData
        };

        ApiService.post(API_MAP.code, data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function deleteCode(codeId) {
        var deferred = $q.defer();

        var data  = {
          session: Auth.session.get(),
          ProductColorLinkedCode: {
            id: codeId
          }
        };

        ApiService.delete(API_MAP.code, data).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
    });

})();