(function() {

  angular.module('printty')

    .factory('ProductionPlaces', function(ApiService, $q, Auth) {

      "ngInject";

      var API_MAP = {
        places: '/production/places',
        placesTypes: '/production/places/types',
        deliveryNotes: '/production/places/deliverynotes',
        csvFileDL: '/production/places/download/delivery/',
        csvFile: '/production/places/delivery/',
        payments: '/production/places/payments',
        status: '/production/places/status',
        shipping: '/production/place/shipping',
        after2: '/production/order/payments',
      };

      return {
        fetchPlaces: fetchPlaces,
        fetchTypes: fetchTypes,
        fetchDeliveryNotes: fetchDeliveryNotes,
        downloadCSV: downloadCSV,
        uploadCSV: uploadCSV,
        downloadPayments: downloadPayments,
        changeStatus: changeStatus,
        fetchInfoCompleteShipping: fetchInfoCompleteShipping,
        updateTrackingNumberOrder: updateTrackingNumberOrder,
        downloadPaymentsAfter2: downloadPaymentsAfter2,

      };

    function changeStatus(data) {
        var postData = {
            session: Auth.session.get(),
            Order: {
                id: data
            }
        };
        return  ApiService.post(API_MAP.status,postData);
    }

      function fetchPlaces(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          {session: Auth.session.get()},
          params
        );

        ApiService.get(API_MAP.places, getParams).then(
          function(data) {

            _.forEach(data.places, function(place) {
              place.selected = (getParams.select_all === 1) ? true : false;
              place.isEmpty = ApiService.toBoolean(place.Place.is_empty);
            });

            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchTypes() {
        var deferred = $q.defer();

        var getParams = {
          session: Auth.session.get()
        };

        ApiService.get(API_MAP.placesTypes, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchDeliveryNotes(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          {session: Auth.session.get()},
          params
        );

        ApiService.post(API_MAP.deliveryNotes, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function downloadCSV(params, type) {
        var deferred = $q.defer();

        var postParams = _.merge(
          {session: Auth.session.get()},
          params
        );

        ApiService.post(API_MAP.csvFileDL + type, postParams).then(
          function(data) {

            var name = ApiService.getFileNameFromUrl(data.url);

            ApiService.download(data.url, {
              name: name,
              type: 'application/zip'
            })
              .then(function(data) {
                deferred.resolve(data);
              })
              .catch(function(error) {
                deferred.reject(error);
              });

          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function downloadPayments(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          {session: Auth.session.get()},
          params
        );

        ApiService.get(API_MAP.payments, getParams).then(
          function(data) {

            var name = ApiService.getFileNameFromUrl(data.url);

            ApiService.download(data.url, {
              name: name,
              type: 'application/zip'
            })
              .then(function(data) {
                deferred.resolve(data);
              })
              .catch(function(error) {
                deferred.reject(error);
              });

          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function downloadPaymentsAfter2(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                {session: Auth.session.get()},
                params
            );

            ApiService.get(API_MAP.after2, getParams).then(
                function(data) {

                    var name = ApiService.getFileNameFromUrl(data.url);

                    ApiService.download(data.url, {
                        name: name,
                        type: 'application/csv'
                    })
                        .then(function(data) {
                            deferred.resolve(data);
                        })
                        .catch(function(error) {
                            deferred.reject(error);
                        });

                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

      function uploadCSV(file, type,factory) {
        var deferred = $q.defer();

        var uploadData = {
          session: Auth.session.get(),
          csv: file,
          factory_id: factory,
        };

        ApiService.upload(API_MAP.csvFile + type, uploadData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function fetchInfoCompleteShipping(params) {
            var deferred = $q.defer();

            var getParams = _.merge({
                session: Auth.session.get()
            }, params);

            ApiService.get(API_MAP.shipping, getParams).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function updateTrackingNumberOrder(order) {
            var deferred = $q.defer();

            var postData = {
                session: Auth.session.get(),
                order: order.Order
            };

            ApiService.post(API_MAP.shipping, postData).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

    });

})();
      