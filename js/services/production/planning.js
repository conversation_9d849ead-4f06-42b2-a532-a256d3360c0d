(function () {

  angular.module('printty')

    .factory('ProductionPlanning', function (ApiService, $q, Auth) {

      "ngInject";

      var API_MAP = {
        unplanned: '/production/planning/unplanned',
        unplannedTypes: '/production/planning/unplanned/types',
        printers: '/production/planning/printers',
        recommendations: '/production/planning/recommendations',
        processToPrinter: '/production/planning/printers/processing',
        processToRecommendations: '/production/planning/recommendations/processing',
        unplannedFilter: '/production/planning/unplanned/filter',
          autoPlanning: '/production/planning/autoPlanning',
          factories: '/factories/user',
      };

      return {
        fetchUnplanned: fetchUnplanned,
        fetchUnplannedTypes: fetchUnplannedTypes,
        fetchPrinters: fetchPrinters,
        fetchRecommendations: fetchRecommendations,
        processToPrinter: processToPrinter,
        processToRecommendations: processToRecommendations,
        fetchUnplannedFilter: fetchUnplannedFilter,
          fetchFactoryAll: fetchFactoryAll,
          autoPlanning:autoPlanning,
      };

        function fetchUnplanned(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.unplanned, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchUnplannedTypes() {
        var deferred = $q.defer();

        var getParams = {
          session: Auth.session.get()
        };

        ApiService.get(API_MAP.unplannedTypes, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function fetchUnplannedFilter() {
            var deferred = $q.defer();

            var getParams = {
                session: Auth.session.get()
            };

            ApiService.get(API_MAP.unplannedFilter, getParams).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function fetchFactoryAll() {
            var deferred = $q.defer();

            var getParams = {
                session: Auth.session.get()
            };

            ApiService.get(API_MAP.factories, getParams).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }
      
      function fetchPrinters(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.printers, getParams).then(
          function(data) {
            
            _.forEach(data.printers, function (printer) {
              printer.note = (+printer.Printer.workload - +printer.Printer.capacity) >= -2;
            });
            
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchRecommendations(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.recommendations, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function autoPlanning(params) {
          var deferred = $q.defer();

          var kornit = (params.kornit !== undefined) ? true : false;

          var putData = {
              session: Auth.session.get(),
              date: params.date,
              order_items: [],
              filterData: params.filterData,
              kornit: kornit,
              factory_id: params.factory_id,
          };

          if (_.isString(params.items)) {
              putData.order_items = params.items;
          } else {

              _.forEach(params.items, function(itemId) {

                  putData.order_items.push(itemId)

              });

          }

          ApiService.post(API_MAP.autoPlanning, putData).then(
              function(data) {
                  deferred.resolve(data);
              },
              function(error) {
                  deferred.reject(error);
              });

          return deferred.promise;
      }
      
      function processToPrinter(params) {
        var deferred = $q.defer();
        
        var putData = {
          session: Auth.session.get(),
          date: params.date,
          Printer: {
            id: params.printerId
          },
          factory_id: params.factory_id,
          order_items: []
        };
        
        if (params.type_code) {
          putData.ProductionType = {
            code: params.type_code
          }
        }
        
        if (_.isString(params.items)) {
          putData.order_items = params.items;
        } else {
          
          _.forEach(params.items, function(itemId) {

            putData.order_items.push({
              OrderItem: {
                id: itemId
              }
            })

          });
          
        }
        
        ApiService.put(API_MAP.processToPrinter, putData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function processToRecommendations(params) {
        var deferred = $q.defer();

        var putData = {
          session: Auth.session.get(),
          date: params.date,
          order_items: []
        };

        if (params.type_code) {
          putData.ProductionType = {
            code: params.type_code
          }
        }

        if (_.isString(params.items)) {
          putData.order_items = params.items;
        } else {

          _.forEach(params.items, function(itemId) {

            putData.order_items.push({
              OrderItem: {
                id: itemId
              }
            })

          });

        }

        ApiService.put(API_MAP.processToRecommendations, putData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

    });

})();
      