(function () {

  angular.module('printty')

    .factory('ProductionPrinters', function (ApiService, $q, Auth) {

      "ngInject";

      var API_MAP = {
        printers: '/production/printers',
        printer: '/production/printers/printer',
        types: '/production/printers/types',
        graph: '/production/printers/graph',
        sheet: '/production/printers/products/sheet',
        qrCodes: '/production/printers/qrcodes',
        qrCodesRemaining: '/production/printers/remaining/qrcodes',
        qrGroups: '/production/printers/groups/qrcodes',
        tasks: '/production/printers/tasks',
        totalGarment: '/production/printers/total',
        periodTotal: '/production/printers/period-total',
      };

      return {
        fetchPrinters: fetchPrinters,
        fetchPrinter: fetchPrinter,
        fetchTypes: fetchTypes,
        fetchGraph: fetchGraph,
        fetchSheet: fetchSheet,
        fetchQrCodes: fetchQrCodes,
        fetchQrCodesRemaining: fetchQrCodesRemaining,
        fetchQrGroups: fetchQrGroups,
        deleteTasks: deleteTasks,
        totalGarment: totalGarment,
        fetchPeriodTotal: fetchPeriodTotal,
      };
      
      function fetchPrinters(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );
        if(!getParams.session) {
            deferred.resolve([]);
            return deferred.promise;
        }

        ApiService.get(API_MAP.printers, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

        function totalGarment(params) {
            var deferred = $q.defer();

            var getParams = {
                session: Auth.session.get(),
                date: params.date
            };

            ApiService.get(API_MAP.totalGarment, getParams).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }
      
        function fetchPeriodTotal(params) {
            var deferred = $q.defer();

            var getParams = {
                session: Auth.session.get(),
                start_date: params.startDate,
                end_date: params.endDate,
                value_type: params.value_type,
                factory_id: params.factory_id,
            };

            ApiService.get(API_MAP.periodTotal, getParams).then(
                function(data) {
                    deferred.resolve(data);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

      function fetchPrinter(params) {
        var deferred = $q.defer();

        var getParams = {
          session: Auth.session.get(),
          printer_id: params.printerId,
          date: params.date
        };

        ApiService.get(API_MAP.printer, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchTypes() {
        var deferred = $q.defer();

        var getParams = {
          session: Auth.session.get()
        };
        if(!getParams.session) {
          deferred.resolve([]);
          return deferred.promise;
        }

        ApiService.get(API_MAP.types, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchGraph(params) {
        var deferred = $q.defer();

        var getParams = {
          session: Auth.session.get(),
          start_date: params.startDate,
          end_date: params.endDate,
          type_id: params.typeId,
          value_type: params.value_type,
          factory_id : params.factory_id,
        };

        ApiService.get(API_MAP.graph, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchSheet(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.sheet, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchQrCodes(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.qrCodes, getParams).then(
          function(data) {
            var codes = _.flatMap(data.qrcodes, function (code) {
              return code.QRCode.content_url;
            });
            
            deferred.resolve(codes);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchQrCodesRemaining(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                { session: Auth.session.get() },
                params
            );

            ApiService.get(API_MAP.qrCodesRemaining, getParams).then(
                function(data) {
                    var codes = _.flatMap(data.qrcodes, function (code) {
                        return code.QRCode.content_url;
                    });

                    deferred.resolve(codes);
                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }
      
      function fetchQrGroups(params) {
        var deferred = $q.defer();

        var getParams = _.merge(
          { session: Auth.session.get() },
          params
        );

        ApiService.get(API_MAP.qrGroups, getParams).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function deleteTasks(params) {
        var deferred = $q.defer();

        var deleteData = {
          session: Auth.session.get(),
          date: params.date
        };
        
        if (params.printers) {

          deleteData.printers = [];
          
          _.forEach(params.printers, function (printerId) {

            deleteData.printers.push({
              Printer: {
                id: printerId
              }
            })
            
          })
          
        }
        
        ApiService.delete(API_MAP.tasks, deleteData).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

    });

})();
      