(function () {

  angular.module('printty')

    .factory('Order', function (ApiService, $q, Auth, $timeout) {

      "ngInject";

      return {
        list: listOrders,
        fetchOrder: fetchOrder,
        statusesAll: statusesAll,
        customerLimit: customerLimit,
        
        deleteOrder: deleteOrder,
        
        fetchDeliveryPeriods: fetchDeliveryPeriods,
        downloadDataStock: downloadDataStock,
        downloadOrderCSV: downloadOrderCSV,
        fetchBilling: fetchBilling,
        downloadCSVBilling: downloadCSVBilling,
        downloadItemInfo: downloadItemInfo,
      };

      function listOrders(params) {
        var deferred = $q.defer();

        params = angular.merge({
          session: Auth.session.get()
        }, params);

        ApiService.get('/orders', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function fetchOrder(orderId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          order_id: orderId
        };

        ApiService.get('/orders/order', params).then(
          function(data) {
            
            data.Order.is_billing_shipping_same = ApiService.toBoolean(data.Order.is_billing_shipping_same);
            data.Order.use_original_tag = ApiService.toBoolean(data.Order.use_original_tag);
            data.Order.use_package = ApiService.toBoolean(data.Order.use_package);
            
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function statusesAll() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/orders/statuses', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }
      
      function customerLimit() {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/customer/limit', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function fetchDeliveryPeriods() {

        var deferred = $q.defer();

        var params = {
          session: Auth.session.get()
        };

        ApiService.get('/orders/order/delivery/periods', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
        
      }
      
      function deleteOrder(orderId) {
        var deferred = $q.defer();

        var params = {
          session: Auth.session.get(),
          Order: {
            id: orderId
          }
        };

        ApiService.delete('/orders/order', params).then(
          function(data) {
            deferred.resolve(data);
          },
          function(error) {
            deferred.reject(error);
          });

        return deferred.promise;
      }

      function downloadOrderCSV(params) {
          var deferred = $q.defer();

          var getParams = _.merge(
              {session: Auth.session.get()},
              params
          );

          ApiService.get('/orders/csv', getParams).then(
              function(data) {

                  var name = ApiService.getFileNameFromUrl(data.url);

                  ApiService.download(data.url, {
                      name: name,
                      type: 'application/csv'
                  })
                      .then(function(data) {
                          deferred.resolve(data);
                      })
                      .catch(function(error) {
                          deferred.reject(error);
                      });

              },
              function(error) {
                  deferred.reject(error);
              });

          return deferred.promise;
      }

      function fetchBilling(params) {
          var deferred = $q.defer();

          var getParams = _.merge(
              {session: Auth.session.get()},
              params
          );

          ApiService.get('/orders/billing', getParams).then(
              function(data) {
                  deferred.resolve(data);
              },
              function(error) {
                  deferred.reject(error);
              });

          return deferred.promise;
      }

        function downloadCSVBilling(params) {
            var deferred = $q.defer();

            var getParams = _.merge(
                {session: Auth.session.get()},
                params
            );

            ApiService.get('/orders/billing/csv', getParams).then(
                function(data) {

                    var name = ApiService.getFileNameFromUrl(data.url);

                    ApiService.download(data.url, {
                        name: name,
                        type: 'application/csv'
                    })
                        .then(function(data) {
                            deferred.resolve(data);
                        })
                        .catch(function(error) {
                            deferred.reject(error);
                        });

                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function downloadDataStock() {
            var deferred = $q.defer();

            var getParams = {
                session: Auth.session.get()
            };

            ApiService.get('/orders/stock/csv', getParams).then(
                function(data) {

                    var name = ApiService.getFileNameFromUrl(data.url);

                    ApiService.download(data.url, {
                        name: name,
                        type: 'application/csv'
                    })
                        .then(function(data) {
                            deferred.resolve(data);
                        })
                        .catch(function(error) {
                            deferred.reject(error);
                        });

                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

        function downloadItemInfo() {
            var deferred = $q.defer();

            var getParams = {
                session: Auth.session.get(),
            };

            ApiService.get('/products/info/csv', getParams).then(
                function(data) {

                    var name = ApiService.getFileNameFromUrl(data.url);

                    ApiService.download(data.url, {
                        name: name,
                        type: 'application/zip'
                    })
                        .then(function(data) {
                            deferred.resolve(data);
                        })
                        .catch(function(error) {
                            deferred.reject(error);
                        });

                },
                function(error) {
                    deferred.reject(error);
                });

            return deferred.promise;
        }

    });

})();
      