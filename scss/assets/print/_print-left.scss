.print-left {
  background: $input-blue-label;
  .logo {
    margin-top: 30px;
    margin-bottom: 5px;
    md-icon {
      width: 80px;
      svg {
        &, & .st0 {
          fill: $black;
        }
      }
    }
  }

  .image-messages {

    .message-inner {
      max-height: 100%;
    }

    .finish-icon {
      width: 170px;
      height: 170px;
      polyline {
        stroke: $blue;
      }
    }

    i.finish-icon {
      font-size: 170px;
      line-height: 170px;
      width: auto;
      height: auto;
      color: $input-error-grey;
    }

    .qr-icon {
      width: 146px;
      height: 146px;
      fill: $blue;
    }
    p {
      font-size: 24px;
      line-height: 1;
      color: $blue;
      margin: 20px 0 0;
    }
  }

  .product-image {
    position: relative;
    > div {
      position: absolute;
      left:0;
      top:0;
      right:0;
      bottom:0;
    }
    img {
      max-width: 90%;
      max-height: 300px;
      min-width: 90px;
    }
    p {
      font-size: 25px;
      margin: 26px 0 0;
      line-height: 1;
    }
  }

  .product-grid {
    position: relative;

    .caption {
      font-size: 23px;
      margin: 27px 0 0;
      line-height: 1;
    }

    table {
      border-collapse: collapse;
      width: 95%;
      img {
        display: block;
        max-width: 100%;
      }
      td {
        height: 0;
        position: relative;
        > div {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          margin: 2.5%;
        }

        &.empty > div {
          background-color: #f5f5f5;
        }

        img, p {
          display: block;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          margin: 0;
          padding: 0;
        }

        p {
          color: $dark;
        }

      }
    }
  }

  .product-image .layout-column {
    padding: 30px;
  }

  .product-image .remark {
    width: 100%;
    margin: 30px;
    min-height: 30%;
    padding: 20px;
    border: 1px solid #afafaf;
    font-size: 20px;
  }

  .product-image .remark2 {
    width: 50%;
    margin-top: 30px;
    min-height: 30%;
    padding: 20px;
    border: 1px solid #afafaf;
    font-size: 18px;
  }

  .product-image .embroidery-image {
    width: 50%;
    margin-top: 30px;
    min-height: 30%;
  }

  .zoom-in-image {
    display: flex;
    width: 100%;
    min-height: 30%;
    overflow: hidden;
  }

}