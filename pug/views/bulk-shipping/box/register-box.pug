section.block-wide(layout='column')
    md-button.md-raised.md-primary.btn-sm.cancel-button(ng-if="vm.box" ng-hide="vm.state.is('manual', 'product')" aria-label='add' ng-click='vm.close()') 戻る
    md-button.md-raised.md-primary.btn-sm.cancel-button(ng-show="vm.state.is('manual', 'product')" ng-click="vm.state.back()") 戻る
    md-button.finish-button(ng-if="vm.box" ng-click='vm.close()') 完了
    .print-container.layout-row(ng-if="vm.box")
        register-box-left.print-left.flex-50.layout-column
        register-box-right.print-right.flex-50.layout-column