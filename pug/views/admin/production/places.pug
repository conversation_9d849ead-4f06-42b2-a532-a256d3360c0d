div.cell.text-center.place-used-current(flex='20' ng-if='vm.used_place.title != null') 現在使用の置き場: {{vm.used_place.title}}
div.search(layout='row', style='position: fixed; background: #fafafa; z-index: 11; width: 89%; padding: 0 37px 0 57px;')
	div.search-form.with-reset.flex-none.layout-row.layout-align-start-center(focusable-search flex='15'): form()
		i.icons8-search
		input.search-input(type='text' ng-model='vm.pagination.searchQuery' ng-change="vm.liveSearch()")
		button.reset(type='button' ng-click="vm.clearSearch()" ng-style='{"left" : 200}'): i.icons8-delete-2
	div.cell.text-center(flex='20' style='margin:15px 0 0 4px')
		md-button.md-raised.md-primary.btn-sm(virtual-repeat-scroll-top-custom) トップに戻る
		md-button.md-raised.md-primary.btn-sm(virtual-repeat-scroll-bottom-custom) 最下部に移動
	div.cell.text-center.flex(style='justify-content: center;align-items: center')
		span(ng-if='vm.showCount' style='padding: 0px 18px;')
			b(style='font-size:15px') 使用中の最終置き場: {{ vm.last_use || 'なし' }}、
		span(ng-if='vm.showCount')
			b(style='font-size:15px') 使用済置き場数: {{ vm.total_use || 0 }}
	div.cell.text-center(ng-style="{'padding-top': '25px'}" ng-if="vm.customerFilter.length")
		md-menu(md-position-mode="target-right target")
			div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeCustomer.Customer.title || '会社名' }}
			md-menu-content.menu-small.custom-checkbox(md-menu-align-target style='width:175px;')
				md-menu-item(ng-repeat='customer in vm.customerFilter')
					md-checkbox(ng-model='customer.selected' ng-change='vm.filterCustomer(customer.Customer.id)') {{ customer.Customer.title }}
	div.cell.text-center(flex='15' style='padding-top:25px; background:#f5f5f5; margin-left:calc(220px + (100vw - 1920px) * (72 / 640)); margin-right:calc(259px + (100vw - 1920px) * (-28 / 640));')
		md-menu(md-position-mode="target-right target")
			div.select-status(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)') {{ vm.activeSort.value }}
			md-menu-content.menu-small(md-menu-align-target)
				md-menu-item(ng-repeat='sort in vm.placesSorts')
					md-button(ng-click='vm.setActiveSort(sort)' ng-disabled='sort.key === vm.activeSort.key') {{ sort.value }}

	div.cell.heading-btns(flex layout='row', layout-align='end center')
		md-button.md-raised.md-primary.btn-sm(ng-click='vm.selectAll()') 全選択
		md-menu(md-position-mode="target-right target")
			md-button.md-raised.md-primary.btn-sm.btn-more.last(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
				i.icons8-more
			md-menu-content.menu-small(md-menu-align-target)
				md-menu-item
					md-menu(md-offset="-20 0")
						a.sub-menu(ng-click='$mdOpenMenu($event)') 納品書
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item
								md-button(ng-click='vm.getDelivery(true)') プレビュー画面の添付
							md-menu-item
								md-menu(md-offset="-20 0")
									a.sub-menu(ng-click='$mdOpenMenu($event)') 納品書ダウンロードカテゴリ選択
									md-menu-content.menu-small(md-menu-align-target ng-style={"padding-left": "8px"})
										md-menu-item
										div.default-row(ng-repeat='placeOption in vm.placeOption[0]')
											md-checkbox(ng-model='placeOption.selected' aria-label='select {{placeOption.id}}') {{ placeOption.title }}
										md-button(ng-click='vm.getDeliveryCategory(true)') プレビュー画面の添付
							md-menu-item
								md-button(ng-click='vm.getDelivery(false)') プレビュー画面を抜く
				md-menu-item
					md-button(ng-click='vm.prePrintGuideNote($event)') 説明書QR発行
				md-menu-item
					md-button(ng-click='vm.getPayments()') 別請求CSVをダウンロード
				md-menu-item
					md-button(ng-click='vm.getPaymentsAfter2()') 後払いCSVのダウンロード
				md-menu-item
					md-menu(md-offset="-20 0")
						a.sub-menu(ng-click='$mdOpenMenu($event)') ヤマト
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item
								md-button(ng-click='vm.CSVdownload(vm.CSVType.yamato)') CSVダウンロード
							md-menu-item
								md-menu(md-offset="-20 0")
									a.sub-menu(ng-click='$mdOpenMenu($event)') CSVダウンロードカテゴリ選択
									md-menu-content.menu-small(md-menu-align-target ng-style={"padding-left": "8px"})
										md-menu-item
										div.default-row(ng-repeat='placeOption in vm.placeOption[0]')
											md-checkbox(ng-model='placeOption.selected' aria-label='select {{placeOption.id}}') {{ placeOption.title }}
										md-button(ng-click='vm.CSVdownload2(vm.CSVType.yamato)') CSVダウンロード
							md-menu-item
								md-button(ngf-select="vm.CSVupload($file,vm.CSVType.yamato)" ) CSVアップロード
				md-menu-item
					md-menu(md-offset="-20 0")
						a.sub-menu(ng-click='$mdOpenMenu($event)') 佐川
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item
								md-button(ng-click='vm.CSVdownload(vm.CSVType.sagawa)') CSVダウンロード
							md-menu-item
								md-menu(md-offset="-20 0")
									a.sub-menu(ng-click='$mdOpenMenu($event)') CSVダウンロードカテゴリ選択
									md-menu-content.menu-small(md-menu-align-target ng-style={"padding-left": "8px"})
										md-menu-item
										div.default-row(ng-repeat='placeOption in vm.placeOption[0]')
											md-checkbox(ng-model='placeOption.selected' aria-label='select {{placeOption.id}}') {{ placeOption.title }}
										md-button(ng-click='vm.CSVdownload2(vm.CSVType.sagawa)') CSVダウンロード
							md-menu-item
								md-button(ngf-select="vm.CSVupload($file,vm.CSVType.sagawa)" ) CSVアップロード
				md-menu-item
					md-menu(md-offset="-20 0")
						a.sub-menu(ng-click='$mdOpenMenu($event)') 日本郵便
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item
								md-button(ng-click='vm.CSVdownload(vm.CSVType.yupacket)') CSVダウンロード
							md-menu-item
								md-menu(md-offset="-20 0")
									a.sub-menu(ng-click='$mdOpenMenu($event)') CSVダウンロードカテゴリ選択
									md-menu-content.menu-small(md-menu-align-target ng-style={"padding-left": "8px"})
										md-menu-item
										div.default-row(ng-repeat='placeOption in vm.placeOption[0]')
											md-checkbox(ng-model='placeOption.selected' aria-label='select {{placeOption.id}}') {{ placeOption.title }}
										md-button(ng-click='vm.CSVdownload2(vm.CSVType.yupacket)') CSVダウンロード
							md-menu-item
								md-button(ngf-select="vm.CSVupload($file,vm.CSVType.yupacket)" ) CSVアップロード
				md-menu-item(ng-if='vm.factory == 1')
					md-menu(md-offset="-20 0")
						a.sub-menu(ng-click='$mdOpenMenu($event)') EMS
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item
								md-button(ng-click='vm.CSVdownload(vm.CSVType.ems)') CSVダウンロード
							md-menu-item
								md-menu(md-offset="-20 0")
									a.sub-menu(ng-click='$mdOpenMenu($event)') CSVダウンロードカテゴリ選択
									md-menu-content.menu-small(md-menu-align-target ng-style={"padding-left": "8px"})
										md-menu-item
										div.default-row(ng-repeat='placeOption in vm.placeOption[0]')
											md-checkbox(ng-model='placeOption.selected' aria-label='select {{placeOption.id}}') {{ placeOption.title }}
										md-button(ng-click='vm.CSVdownload2(vm.CSVType.ems)') CSVダウンロード
							md-menu-item
								md-button(ngf-select="vm.CSVupload($file,vm.CSVType.ems)" ) CSVアップロード

				md-menu-item(ng-if='vm.factory == 1')
					md-menu(md-offset="-20 0")
						a.sub-menu(ng-click='$mdOpenMenu($event)') 海外
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item
								md-button(ng-click='vm.CSVdownload(vm.CSVType.abroad)') CSVダウンロード
							md-menu-item
								md-button(ngf-select="vm.CSVupload($file,vm.CSVType.abroad)" ) CSVアップロード
md-virtual-repeat-container.content(flex='grow' )

	div.search(layout='row')

	md-divider

	div.default-row(md-virtual-repeat='place in vm.infinitePlaces' md-on-demand ng-show='place', ng-switch='place.isEmpty')

		div.layout-row.layout-align-center-center(ng-switch-when="true")
			div.cell.cell-data(flex=60 layout='row' layout-align='start center' layout-wrap)
				div: md-checkbox(ng-model='place.selected' aria-label='select place')
				.data-rowed(layout='row', layout-align='start center')
					div {{ place.Place.title }}
					.divider
					div 空

			.cell.text-center(flex='15') {{ place.Place.status }}

			div.cell.cell-action(flex, layout='row', layout-align='end center')
				div.buttons
					md-menu(md-position-mode="target-right target")
						md-button.md-raised.md-primary.btn-sm.btn-more(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
							i.icons8-more
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item
								md-menu(md-offset="-20 0")
									a.sub-menu(ng-click='$mdOpenMenu($event)') 納品書
									md-menu-content.menu-small(md-menu-align-target)
										md-menu-item
											md-button(ng-click='vm.getDelivery(true)') プレビュー画面の添付
										md-menu-item
											md-button(ng-click='vm.getDelivery(false)') プレビュー画面を抜く
							md-menu-item
								md-button(ng-click='vm.changeStatus($event, place)') ステータス変更
							md-menu-item
								md-button(ng-click='vm.manualShippingComplete(place)') 手入力で発送番号・メモを入力する
		
		div.layout-row.layout-align-center-center(ng-switch-when="false")
			div.cell.cell-data(flex=60 layout='row' layout-align='start center' layout-wrap)
				div: md-checkbox(ng-model='place.selected' aria-label='select place')
				.data-rowed(layout='row', layout-align='start center')
					div {{ place.Place.title }}
					.divider
					div {{ place.Order.number }}
					.divider.addition
					.addition {{ place.Customer.title }}
					.divider.addition
					.addition 合計数量: {{ place.Order.total_quantity }}
					.divider(ng-if="place.Order.memo").addition
					.addition {{place.Order.memo}}
					.divider(ng-if="place.Place.has_same_day == true")
					.div.flex.align-item-center(ng-if='vm.gift_display && ( place.Order.gift_pink > 0 || place.Order.gift_blue > 0 || place.Order.gift_yellow > 0 )')
						.divider
						|ギフト(
						span(ng-if="place.Order.gift_pink > 0") ピンク: {{ place.Order.gift_pink }}
							span(ng-if='place.Order.gift_blue > 0 || place.Order.gift_yellow > 0') 、
						span(ng-if="place.Order.gift_blue > 0") 青：{{ place.Order.gift_blue }}
							span(ng-if='place.Order.gift_yellow > 0') 、
						span(ng-if="place.Order.gift_yellow > 0") 黄：{{ place.Order.gift_yellow }}
						span )
					div(ng-if="place.Place.has_same_day == true") 【即日あり】
			.cell.text-center(flex='15') {{ place.Place.status }}
						
			div.cell.cell-action(flex, layout='row', layout-align='end center')
				div.buttons
					md-menu(md-position-mode="target-right target")
						md-button.md-raised.md-primary.btn-sm.btn-more(md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)')
							i.icons8-more
						md-menu-content.menu-small(md-menu-align-target)
							md-menu-item
								md-menu(md-offset="-20 0")
									a.sub-menu(ng-click='$mdOpenMenu($event)') 納品書
									md-menu-content.menu-small(md-menu-align-target)
										md-menu-item
											md-button(ng-click='vm.getDelivery(true)') プレビュー画面の添付
										md-menu-item
											md-button(ng-click='vm.getDelivery(false)') プレビュー画面を抜く
							md-menu-item
								md-button(ng-click='vm.changeStatus($event, place)') ステータス変更
							md-menu-item
								md-button(ng-click='vm.manualShippingComplete(place)') 手入力で発送番号・メモを入力する
								
	div.pagination-loading(ng-show='vm.pagination.loading')
		md-progress-circular(md-diameter='62' md-mode='indeterminate')

.highlighted-column-container(flex layout='row', ng-hide='vm.pagination.empty'): .highlighted-column(flex=15 flex-offset=60)
div(ng-if="vm.gift_display" style="position: fixed;top: 70px;margin-left: 44%;")
	b  ＜ギフト＞ ピンク：{{vm.gift_pink || 0}}、青：{{vm.gift_blue || 0}}、黄：{{vm.gift_yellow || 0}}

.nothing-found-msg(ng-if='vm.pagination.empty'): div 何も見つかりませんでした
md-select(ng-model='vm.factory' name='factory' aria-label='Select Factory' ng-change='vm.onChange()' style="position: fixed;top: 0;left: 400px;margin-top:24px")
	md-option(ng-repeat='factory in vm.factories' ng-value='factory.id') {{ factory.title }}

manual-shipping-complete-details(
	is-shown='vm.manualShippingCompleteDetails.isShown',
	active='vm.manualShippingCompleteDetails.active',
	type='vm.manualShippingCompleteDetails.type',
	on-close='vm.onCompleteShippingClose()',
	on-update='vm.onCompleteShippingUpdate(order)',
)