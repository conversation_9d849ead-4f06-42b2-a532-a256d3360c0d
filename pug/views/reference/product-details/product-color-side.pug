div.block-wide(layout='column', ng-hide='vm.linkPlateBlock.isShown')

	header(flex='none')
		button.go-back-button(type='button' ng-click='vm.close()'): md-icon(md-svg-src='img/icons/back.svg' aria-label='return')
		h2 {{ vm.isAdd ? 'サイド追加' : 'サイド情報' }}
		div.sidenav-actions(ng-hide='vm.isAdd' ng-if="vm.showButton")
			button.btn-edit.simple-btn(type='button' ng-click='vm.edit()'): i.icons8-edit
			button.btn-delete.simple-btn(type='button' ng-click='vm.confirmDelete($event)'): i.icons8-delete

	md-content(flex='grow')
		.color-block.bottom-border

			div.add-color
				form.form.default-form(layout='row' layout-wrap name='productColorSideForm')
					div.input-container(flex=100 ng-if='!vm.isAdd')
						md-input-container
							label ID
							input(type='text' ng-model='vm.side.id' readonly)
					div.input-container(flex=100)
						md-input-container
							label タイトル
							input(type='text' ng-model='vm.side.title' name='side-title' ng-readonly='vm.showMode' required)
					div.input-container.bottom-border(flex=100)
						md-input-container
							label JSON
							textarea(md-no-resize max-rows=7 ng-model='vm.side.content' name='side-json' ng-readonly='vm.showMode' required)
							
		// product linked code block
		.sidenav-list-block(ng-show='vm.showMode && !vm.isAdd')

			h3 連携設定

			.sidenav-list
				div.heading.layout-row.layout-align-start-center
					div.cell.flex サイズ
					div.cell.flex-none プラテン
				div.default-row.layout-row.layout-align-start-center(ng-repeat='plate in vm.plates')
					div.cell.flex {{ plate.ProductSize.title }}
					div.cell.flex-none.layout-row.layout-align-end-center 
						div {{ plate.Plate.title }}
						md-menu.small(md-position-mode="target-right target")
							button.more-btn.simple-icon-btn(type='button' md-menu-align-target aria-label='more' ng-click='$mdOpenMenu($event)'): i.icons8-more
							md-menu-content.menu-small(md-menu-align-target)
								md-menu-item: md-button(ng-click='vm.linkPlateBlock.show(plate)') 詳細

			div.text-center: md-button.simple-button.btn-sm(ng-click='vm.linkPlateBlock.add()' ng-if="vm.showButton") 連携設定を追加

	footer.text-right(flex='none' ng-hide='vm.showMode')
		div(ng-show='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.save()') 保存
		div(ng-hide='vm.isAdd'): md-button.md-raised.md-accent(ng-click='vm.update()') 保存
		
		
product-color-side-linked-plate-details(
	ng-show='vm.linkPlateBlock.isShown',
	loading-type='vm.loadingType'
	type='vm.linkPlateBlock.type',
	active='vm.linkPlateBlock.activeItem',
	on-create='vm.linkPlateBlock.onCreate()',
	on-update='vm.linkPlateBlock.onUpdate(plate)',
	on-delete='vm.linkPlateBlock.onDelete(plateId)',
	on-close='vm.linkPlateBlock.onClose()',
	show-button='vm.showButton',
)