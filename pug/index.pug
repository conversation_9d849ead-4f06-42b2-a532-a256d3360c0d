doctype html
html(lang="ja" ng-app="printty")
	head
		meta(charset="utf-8")
		title Printty

		// loader styles
		style.
			.loader-container {
				display: block;
				background-color: #eee;
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 999999;
			}

			.loader {
				color: #424242;
				font-size: 24px;
				font-weight: 700;
				font-family: sans-serif;
				transform: translate(-50%, -50%);
			}

			.loader, .loader:after {
				position: absolute;
				top: 50%;
				left: 50%;
			}

			.loader:after {
				content: '';
				display: block;
				width: 200px;
				height: 200px;
				border: 4px solid #424242;
				border-top: 4px solid transparent;
				border-bottom: 4px solid transparent;
				border-radius: 50%;
				margin: -100px 0 0 -100px;
				transform: translate(-50%, -50%);
				animation: rotate 2s infinite;
			}

			@keyframes rotate {
				0% {
					transform: rotate(0deg);
				}
				100% {
					transform: rotate(180deg);
				}
			}

		// build:css css/all.min.css
		link(rel='stylesheet', href='css/angular-material.css')
		link(rel='stylesheet', href='css/icons.css')
		link(rel='stylesheet', href='css/main.css')
		link(rel='stylesheet', href='node_modules/angular-material-time-picker/dist/md-time-picker.css')
		// endbuild

		link(href='https://fonts.googleapis.com/icon?family=Material+Icons', rel='stylesheet')

	body
		.loader-container
			.loader 読み込み中
		div#not-supported-msg
			div
				| こちらのブラウザで利用できません。
				br
				| 最新型ブラウザに更新してください。
		div.root(ui-view)

		.global-loading
			md-progress-circular(md-diameter='80' md-mode='indeterminate')
		.global-loading-progress
			md-progress-circular(md-mode="determinate" md-diameter='80' value="{{$root.progressValue}}")
		//- scripts

		// build:js js/vendor.min.js
		script(src='bower_components/jquery/dist/jquery.js')
		script(src='bower_components/lodash/dist/lodash.js')
		script(src='bower_components/moment/min/moment.min.js')
		script(src='bower_components/moment/locale/ja.js')
		script(src='bower_components/clipboard/dist/clipboard.js')
		script(src='bower_components/chart.js/dist/Chart.min.js')
		script(src='js/plugins/drawtool.min.js')
		script(src='bower_components/qrcode-generator/js/qrcode.js')
		script(src='bower_components/file-saver/FileSaver.js')

		script(src='bower_components/angular/angular.js')
		script(src='bower_components/angular-cookies/angular-cookies.js')
		script(src='bower_components/angular-animate/angular-animate.js')
		script(src='bower_components/angular-aria/angular-aria.js')
		script(src='bower_components/angular-messages/angular-messages.js')
		script(src='bower_components/angular-material/angular-material.js')
		script(src='bower_components/angular-ui-router/release/angular-ui-router.js')
		script(src='bower_components/ngclipboard/dist/ngclipboard.js')
		script(src='bower_components/ng-file-upload/ng-file-upload.js')
		script(src='bower_components/ngInfiniteScroll/build/ng-infinite-scroll.js')
		script(src='bower_components/angular-qrcode/angular-qrcode.js')
		script(src='vendor/angular-multiple-date-picker/dist/multipleDatePicker.js')
		script(src='node_modules/angular-paging/dist/paging.js')
		script(src='js/plugins/md-time-picker.js')
		script(src='node_modules/jquery-ui-dist/jquery-ui.min.js')
		script(src='js/plugins/sortable.js')

		if !electron
			script(src='js/electron/dummy.js')
		// endbuild

		if electron
			script(src='js/electron/electangular.js')

		// build:js js/app.min.js
		script(src='js/extensions.js')

		script(src='js/app.js')

		script(src='js/config/routes.js')
		script(src='js/config/config.js')

		script(src='js/filters/filters.js')

		script(src='js/components/general/root.js')
		script(src='js/components/login/login.js')

		script(src='js/components/common/common.js')
		script(src='js/components/common/profile.js')
		script(src='js/components/common/orders/order-detail.js')
		script(src='js/components/common/orders/orders-list.js')
		script(src='js/components/common/orders/orders-history.js')
		script(src='js/components/common/callback-option.js')

		script(src='js/components/admin/general.js')
		script(src='js/components/admin/blank.js')
		script(src='js/components/admin/global-search.js')
		script(src='js/components/admin/admin-calendar.js')
		script(src='js/components/admin/income/income.js')
		script(src='js/components/admin/income/clients.js')
		script(src='js/components/admin/income/client-details.js')
		//- script(src='js/components/admin/income/companies.js')
		script(src='js/components/admin/income/change-price.js')
		script(src='js/components/admin/income/price-details.js')
		script(src='js/components/admin/income/price-customer-details.js')
		script(src='js/components/admin/income/order-detail.js')

		script(src='js/components/admin/production/production.js')
		script(src='js/components/admin/production/printers/printers.js')
		script(src='js/components/admin/production/printers/printer-info.js')
		script(src='js/components/admin/production/printers/printer-task-details.js')
		script(src='js/components/admin/production/printers/printers-chart.js')
		script(src='js/components/admin/production/places.js')
		script(src='js/components/admin/production/manual-shipping-complete-details.js')
		script(src='js/components/admin/production/tasks/tasks.js')
		script(src='js/components/admin/production/tasks/task-details.js')
		script(src='js/components/admin/production/waiting.js')
		script(src='js/components/admin/production/create-plan.js')
		script(src='js/components/admin/production/printers/printer-product-sheet.js')
		script(src='js/components/admin/production/printers/printer-qr-groups.js')

		script(src='js/components/admin/users/user-details.js')
		script(src='js/components/admin/users/users.js')

		script(src='js/components/admin/error-log/error-logs.js')
		script(src='js/components/admin/error-log/error-log-details.js')

		script(src='js/components/admin/body-order/body-order.js')
		script(src='js/components/admin/image-error/image-error.js')

		script(src='js/components/admin/order-status-search/order-status-search.js')

		//- script(src='js/components/dashboard/dashboard.js')
		//- script(src='js/components/dashboard/main-chart.js')
		//- script(src='js/components/dashboard/pie-chart.js')

		script(src='js/components/print/print.js')
		script(src='js/components/dtf-transcription/dtf-transcription.js')
		script(src='js/components/print/print-download.js')
		script(src='js/components/printoriginal/print-original.js')
		script(src='js/components/printoriginal/print-original-download.js')

		script(src='js/components/delivery/delivery.js')
		script(src='js/components/kenpin/kenpin.js')
		script(src='js/components/kenpin/kenpin-failed.js')
		script(src='js/components/kenpini/kenpini.js')
		script(src='js/components/kenpini/kenpini-failed.js')
		script(src='js/components/kenpinuv/kenpinuv.js')
		script(src='js/components/kenpinuv/kenpinuv-failed.js')

		script(src='js/components/random/random.js')
		script(src='js/components/newrandom/newrandom.js')
		script(src='js/components/andonkai/andonkai.js')
		script(src='js/components/seirenrandom/seirenrandom.js')
		script(src='js/components/randomuv/randomuv.js')
		script(src='js/components/totalrandom/totalrandom.js')
		script(src='js/components/andonstatistical/andonstatistical.js')

		script(src='js/components/user/general.js')
		script(src='js/components/user/orders.js')
		script(src='js/components/user/history.js')
		script(src='js/components/user/order-detail.js')
		script(src='js/components/user/new-order.js')
		script(src='js/components/user/product-type.js')
		script(src='js/components/user/billing.js')
		script(src='js/components/user/blank.js')

		script(src='js/components/storage/storage.js')
		script(src='js/components/storage/current.js')
		script(src='js/components/storage/current-detail.js')
		script(src='js/components/storage/product-history.js')
		script(src='js/components/storage/csv-upload.js')
		script(src='js/components/storage/activity.js')
		script(src='js/components/storage/matrix.js')
		script(src='js/components/storage/picking.js')
		script(src='js/components/storage/certificate.js')
		script(src='js/components/storage/certificate-details.js')
		script(src='js/components/storage/certificate-detail.js')
		script(src='js/components/storage/activity-details.js')
		script(src='js/components/storage/picking-vakuum.js')
		script(src='js/components/storage/supplier/suppliers.js')
		script(src='js/components/storage/supplier/supplier-detail.js')
		script(src='js/components/storage/storage-items/storage-items.js')
		script(src='js/components/storage/storage-items/add-storage-item.js')
		script(src='js/components/storage/storage-items/edit-storage-item.js')
		script(src='js/components/storage/option-items/option-items.js')
		script(src='js/components/storage/option-items/option-items-detail.js')
		script(src='js/components/storage/option-items/option-items-history.js')
		script(src='js/components/storage/option-items/option-items-picking.js')
		script(src='js/components/storage/option-items/option-items-activity.js')

		script(src='js/components/reference/reference.js')
		script(src='js/components/reference/printers.js')
		script(src='js/components/reference/products.js')
		script(src='js/components/reference/places.js')
		script(src='js/components/reference/add-printer.js')
		script(src='js/components/reference/edit-printer.js')
		script(src='js/components/reference/add-place.js')
		script(src='js/components/reference/edit-place.js')
		script(src='js/components/reference/plates/plates.js')
		script(src='js/components/reference/plates/plate-details.js')
		script(src='js/components/reference/product-details/product.js')
		script(src='js/components/reference/product-details/product-linked-code.js')
		script(src='js/components/reference/product-details/product-size.js')
		script(src='js/components/reference/product-details/product-color.js')
		script(src='js/components/reference/product-details/product-color-side.js')
		script(src='js/components/reference/product-details/product-size-linked-code.js')
		script(src='js/components/reference/product-details/product-color-linked-code.js')
		script(src='js/components/reference/product-details/product-color-side-linked-plate.js')
		script(src='js/components/reference/product-details/product-fee-design.js')
		script(src='js/components/reference/printing-bases/printing-bases.js')
		script(src='js/components/reference/printing-bases/printing-base-details.js')
		script(src='js/components/reference/uv-frames/uv-frames.js')
		script(src='js/components/reference/uv-frames/uv-frame-details.js')
		script(src='js/components/reference/cp-frames/cp-frames.js')
		script(src='js/components/reference/cp-frames/cp-frame-details.js')
		script(src='js/components/reference/reasons/reasons.js')
		script(src='js/components/reference/reasons/add-reason.js')
		script(src='js/components/reference/reasons/edit-reason.js')
		script(src='js/components/reference/clients/clients.js')
		script(src='js/components/reference/clients/client-details.js')
		script(src='js/components/reference/clients-kornit/clients-kornit.js')
		script(src='js/components/reference/clients-kornit/client-kornit-details.js')
		script(src='js/components/reference/trims/trims.js')
		script(src='js/components/reference/warehouses/warehouses.js')
		script(src='js/components/reference/warehouses/add-warehouse.js')
		script(src='js/components/reference/warehouses/edit-warehouse.js')
		script(src='js/components/reference/categories/categories.js')
		script(src='js/components/reference/categories/category-details.js')
		script(src='js/components/reference/image-name/image-name.js')
		script(src='js/components/reference/image-name/image-name-details.js')
		script(src='js/components/reference/order-product-code/order-product-code.js')
		script(src='js/components/reference/order-product-code/order-product-code-details.js')
		script(src='js/components/reference/price-csv/price-csv.js')
		script(src='js/components/reference/product-cell/product-cell.js')
		script(src='js/components/reference/product-cell/product-cell-details.js')
		script(src='js/components/reference/factories/factories.js')
		script(src='js/components/reference/factories/factory-details.js')
		script(src='js/components/reference/andon/andon.js')
		script(src='js/components/reference/andon/andon-details.js')
		script(src='js/components/reference/option-items/option-items.js')
		script(src='js/components/reference/option-items/option-items-detail.js')

		//image
		script(src='js/components/image/image.js')
		script(src='js/components/image/single.js')
		script(src='js/components/image/group.js')
		script(src='js/components/image/delivery.js')
		script(src='js/components/image/qrgroup.js')
		script(src='js/components/image/uv.js')
		script(src='js/components/image/sublimation.js')
		script(src='js/components/image/sublimation_noto.js')
		script(src='js/components/image/dtf.js')
		script(src='js/components/image/dtf_noto.js')
		script(src='js/components/image/dtf_seiren.js')
		script(src='js/components/image/dtf_second.js')
		script(src='js/components/image/paper_print.js')
		script(src='js/components/image/data_3d.js')
		script(src='js/components/image/external_factory_print_data.js')
		script(src='js/components/image/material.js')
		script(src='js/components/image/textile.js')

		//import
		script(src='js/components/import/import.js')

		//item
		script(src='js/components/import/item/item.js')
		script(src='js/components/import/item/product.js')
		script(src='js/components/import/item/product-detail.js')
		script(src='js/components/import/item/source.js')
		script(src='js/components/import/item/source-detail.js')
		script(src='js/components/import/item/uvframe.js')
		script(src='js/components/import/item/uvframe-detail.js')
		script(src='js/components/import/item/cpframe.js')
		script(src='js/components/import/item/cpframe-detail.js')
		script(src='js/components/import/item/printingbase.js')
		script(src='js/components/import/item/printingbase-detail.js')

		//other
		script(src='js/components/import/other/other.js')
		script(src='js/components/import/other/color.js')
		script(src='js/components/import/other/color-detail.js')
		script(src='js/components/import/other/size.js')
		script(src='js/components/import/other/size-detail.js')
		script(src='js/components/import/other/uv.js')
		script(src='js/components/import/other/uv-detail.js')
		script(src='js/components/import/other/side.js')
		script(src='js/components/import/other/side-detail.js')
		script(src='js/components/import/other/plate.js')
		script(src='js/components/import/other/plate-detail.js')
		script(src='js/components/import/other/vakuum.js')
		script(src='js/components/import/other/vakuum-detail.js')
		script(src='js/components/import/other/cp.js')
		script(src='js/components/import/other/cp-detail.js')
		script(src='js/components/import/other/option-item.js')

		// quality
		script(src='js/components/quality/quality.js')
		script(src='js/components/quality/list/quality-detail.js')
		script(src='js/components/quality/list/list.js')
		script(src='js/components/quality/list/chart/pie.js')
		script(src='js/components/quality/list/chart/bar.js')


		//	 repair
		script(src='js/components/repair/repair.js')
		script(src='js/services/repair-api.js')
		script(src='js/components/reference/sources/sources.js')
		script(src='js/components/reference/sources/source-details.js')

		// business
		script(src='js/components/business/business.js')
		script(src='js/services/business-api.js')

		// barcode
		script(src='js/components/barcode/barcode.js')
		script(src='js/services/barcode-api.js')

		// bulk shipping
		script(src='js/components/bulk-shipping/box/box.js')
		script(src='js/components/bulk-shipping/box/box-details.js')
		script(src='js/components/bulk-shipping/general.js')
		script(src='js/components/bulk-shipping/register-box.js')
		script(src='js/services/bulk-shipping-api.js')

		//factory-order
		script(src='js/components/factory-order/factory-order.js')
		script(src='js/components/factory-order/factory.js')
		script(src='js/components/factory-order/factory-details.js')
		script(src='js/services/factory-order-api.js')

		script(src='js/directives/common.js')
		script(src='js/directives/product.js')
		script(src='js/directives/loading.js')

		script(src='js/services/utilities.js')
		script(src='js/services/api-service.js')
		script(src='js/services/auth.js')
		script(src='js/services/store.js')
		script(src='js/services/place.js')
		script(src='js/services/printer.js')
		script(src='js/services/storage.js')
		script(src='js/services/supplier.js')
		script(src='js/services/drawer.js')
		script(src='js/services/print-api.js')
		script(src='js/services/dtf-transcription-api.js')
		script(src='js/services/order.js')
		script(src='js/services/order-item.js')
		script(src='js/services/product.js')
		script(src='js/services/product-size.js')
		script(src='js/services/product-color.js')
		script(src='js/services/product-linked-code.js')
		script(src='js/services/product-calendar.js')
		script(src='js/services/plate.js')
		script(src='js/services/production/printers.js')
		script(src='js/services/production/tasks.js')
		script(src='js/services/production/planning.js')
		script(src='js/services/production/unplanned.js')
		script(src='js/services/production/places.js')
		script(src='js/services/customers.js')
		script(src='js/services/factory.js')
		script(src='js/services/customers-orders.js')
		script(src='js/services/users.js')
		script(src='js/services/search.js')
		script(src='js/services/printing-base.js')
		script(src='js/services/uv-frames.js')
		script(src='js/services/cp-frames.js')
		script(src='js/services/delivery.js')
		script(src='js/services/kenpin-api.js')
		script(src='js/services/kenpini-api.js')
		script(src='js/services/kenpinuv-api.js')
		script(src='js/services/random-api.js')
		script(src='js/services/newrandom-api.js')
		script(src='js/services/andon-api.js')
		script(src='js/services/seirenrandom-api.js')
		script(src='js/services/totalrandom-api.js')
		script(src='js/services/randomuv-api.js')
		script(src='js/services/image.js')
		script(src='js/services/import/source.js')
		script(src='js/services/import/color.js')
		script(src='js/services/import/size.js')
		script(src='js/services/import/uv.js')
		script(src='js/services/import/vakuum.js')
		script(src='js/services/import/cp.js')
		script(src='js/services/import/side.js')
		script(src='js/services/import/plate.js')
		script(src='js/services/import/product.js')
		script(src='js/services/quality.js')
		script(src='js/services/storage-activity-content.js')
		script(src='js/services/storage-item-api.js')
		script(src='js/services/clients.js')
		script(src='js/services/error-logs.js')
		script(src='js/services/image-error.js')
		script(src='js/services/order-status-search.js')
		script(src='js/services/source.js')
		script(src='js/services/categories.js')
		script(src='js/services/image-name.js')
		script(src='js/services/image-delivery.js')
		script(src='js/services/image-qrgroup.js')
		script(src='js/services/image-uv.js')
		script(src='js/services/order-product-code-api.js')
		script(src='js/services/body-order-api.js')
		script(src='js/services/price-csv-api.js')
		script(src='js/services/export-data-api.js')
		script(src='js/services/product-cell-api.js')
		script(src='js/services/option-items.js')
		script(src='js/services/storage-option.js')
		// endbuild

		// build:js js/api-config.js
		script(src='js/config/api.js')
		// endbuild

		if !electron
			// build:js local_config/chart.js
			script(src='local_config/chart.js')
			//endbuild